bin/bootstrapping/gsutil.py
data/cli/gsutil.json
platform/gsutil/CHANGES.md
platform/gsutil/CHECKSUM
platform/gsutil/CONTRIBUTING.md
platform/gsutil/LICENSE
platform/gsutil/MANIFEST.in
platform/gsutil/README.md
platform/gsutil/VERSION
platform/gsutil/gslib/README
platform/gsutil/gslib/__init__.py
platform/gsutil/gslib/__main__.py
platform/gsutil/gslib/addlhelp/__init__.py
platform/gsutil/gslib/addlhelp/acls.py
platform/gsutil/gslib/addlhelp/command_opts.py
platform/gsutil/gslib/addlhelp/crc32c.py
platform/gsutil/gslib/addlhelp/creds.py
platform/gsutil/gslib/addlhelp/dev.py
platform/gsutil/gslib/addlhelp/encoding.py
platform/gsutil/gslib/addlhelp/metadata.py
platform/gsutil/gslib/addlhelp/naming.py
platform/gsutil/gslib/addlhelp/prod.py
platform/gsutil/gslib/addlhelp/security.py
platform/gsutil/gslib/addlhelp/shim.py
platform/gsutil/gslib/addlhelp/support.py
platform/gsutil/gslib/addlhelp/versions.py
platform/gsutil/gslib/boto_resumable_upload.py
platform/gsutil/gslib/boto_translation.py
platform/gsutil/gslib/bucket_listing_ref.py
platform/gsutil/gslib/cloud_api.py
platform/gsutil/gslib/cloud_api_delegator.py
platform/gsutil/gslib/command.py
platform/gsutil/gslib/command_argument.py
platform/gsutil/gslib/command_runner.py
platform/gsutil/gslib/commands/__init__.py
platform/gsutil/gslib/commands/acl.py
platform/gsutil/gslib/commands/autoclass.py
platform/gsutil/gslib/commands/bucketpolicyonly.py
platform/gsutil/gslib/commands/cat.py
platform/gsutil/gslib/commands/compose.py
platform/gsutil/gslib/commands/config.py
platform/gsutil/gslib/commands/cors.py
platform/gsutil/gslib/commands/cp.py
platform/gsutil/gslib/commands/defacl.py
platform/gsutil/gslib/commands/defstorageclass.py
platform/gsutil/gslib/commands/du.py
platform/gsutil/gslib/commands/hash.py
platform/gsutil/gslib/commands/help.py
platform/gsutil/gslib/commands/hmac.py
platform/gsutil/gslib/commands/iam.py
platform/gsutil/gslib/commands/kms.py
platform/gsutil/gslib/commands/label.py
platform/gsutil/gslib/commands/lifecycle.py
platform/gsutil/gslib/commands/logging.py
platform/gsutil/gslib/commands/ls.py
platform/gsutil/gslib/commands/mb.py
platform/gsutil/gslib/commands/mv.py
platform/gsutil/gslib/commands/notification.py
platform/gsutil/gslib/commands/pap.py
platform/gsutil/gslib/commands/perfdiag.py
platform/gsutil/gslib/commands/rb.py
platform/gsutil/gslib/commands/requesterpays.py
platform/gsutil/gslib/commands/retention.py
platform/gsutil/gslib/commands/rewrite.py
platform/gsutil/gslib/commands/rm.py
platform/gsutil/gslib/commands/rpo.py
platform/gsutil/gslib/commands/rsync.py
platform/gsutil/gslib/commands/setmeta.py
platform/gsutil/gslib/commands/signurl.py
platform/gsutil/gslib/commands/stat.py
platform/gsutil/gslib/commands/test.py
platform/gsutil/gslib/commands/ubla.py
platform/gsutil/gslib/commands/update.py
platform/gsutil/gslib/commands/version.py
platform/gsutil/gslib/commands/versioning.py
platform/gsutil/gslib/commands/web.py
platform/gsutil/gslib/context_config.py
platform/gsutil/gslib/cred_types.py
platform/gsutil/gslib/cs_api_map.py
platform/gsutil/gslib/daisy_chain_wrapper.py
platform/gsutil/gslib/data/cacerts.txt
platform/gsutil/gslib/devshell_auth_plugin.py
platform/gsutil/gslib/discard_messages_queue.py
platform/gsutil/gslib/exception.py
platform/gsutil/gslib/file_part.py
platform/gsutil/gslib/gcs_json_api.py
platform/gsutil/gslib/gcs_json_credentials.py
platform/gsutil/gslib/gcs_json_media.py
platform/gsutil/gslib/help_provider.py
platform/gsutil/gslib/iamcredentials_api.py
platform/gsutil/gslib/impersonation_credentials.py
platform/gsutil/gslib/kms_api.py
platform/gsutil/gslib/lazy_wrapper.py
platform/gsutil/gslib/metrics.py
platform/gsutil/gslib/metrics_reporter.py
platform/gsutil/gslib/metrics_tuple.py
platform/gsutil/gslib/name_expansion.py
platform/gsutil/gslib/no_op_auth_plugin.py
platform/gsutil/gslib/no_op_credentials.py
platform/gsutil/gslib/parallel_tracker_file.py
platform/gsutil/gslib/plurality_checkable_iterator.py
platform/gsutil/gslib/progress_callback.py
platform/gsutil/gslib/project_id.py
platform/gsutil/gslib/pubsub_api.py
platform/gsutil/gslib/resumable_streaming_upload.py
platform/gsutil/gslib/seek_ahead_thread.py
platform/gsutil/gslib/sig_handling.py
platform/gsutil/gslib/storage_uri_builder.py
platform/gsutil/gslib/storage_url.py
platform/gsutil/gslib/tab_complete.py
platform/gsutil/gslib/tests/__init__.py
platform/gsutil/gslib/tests/mock_cloud_api.py
platform/gsutil/gslib/tests/mock_logging_handler.py
platform/gsutil/gslib/tests/rewrite_helper.py
platform/gsutil/gslib/tests/signurl_signatures.py
platform/gsutil/gslib/tests/test_Doption.py
platform/gsutil/gslib/tests/test_acl.py
platform/gsutil/gslib/tests/test_autoclass.py
platform/gsutil/gslib/tests/test_boto_util.py
platform/gsutil/gslib/tests/test_bucketconfig.py
platform/gsutil/gslib/tests/test_bucketpolicyonly.py
platform/gsutil/gslib/tests/test_cat.py
platform/gsutil/gslib/tests/test_cloud_api_delegator.py
platform/gsutil/gslib/tests/test_command.py
platform/gsutil/gslib/tests/test_command_runner.py
platform/gsutil/gslib/tests/test_compose.py
platform/gsutil/gslib/tests/test_context_config.py
platform/gsutil/gslib/tests/test_copy_helper_funcs.py
platform/gsutil/gslib/tests/test_copy_objects_iterator.py
platform/gsutil/gslib/tests/test_cors.py
platform/gsutil/gslib/tests/test_cp.py
platform/gsutil/gslib/tests/test_creds_config.py
platform/gsutil/gslib/tests/test_daisy_chain_wrapper.py
platform/gsutil/gslib/tests/test_data/favicon.ico.gz
platform/gsutil/gslib/tests/test_data/test.gif
platform/gsutil/gslib/tests/test_data/test.json
platform/gsutil/gslib/tests/test_data/test.mp3
platform/gsutil/gslib/tests/test_data/test.p12
platform/gsutil/gslib/tests/test_data/test.txt
platform/gsutil/gslib/tests/test_data/test_external_account_authorized_user_credentials.json
platform/gsutil/gslib/tests/test_data/test_external_account_credentials.json
platform/gsutil/gslib/tests/test_defacl.py
platform/gsutil/gslib/tests/test_defstorageclass.py
platform/gsutil/gslib/tests/test_du.py
platform/gsutil/gslib/tests/test_encryption_helper.py
platform/gsutil/gslib/tests/test_execution_util.py
platform/gsutil/gslib/tests/test_file_part.py
platform/gsutil/gslib/tests/test_gcs_json_api.py
platform/gsutil/gslib/tests/test_gcs_json_credentials.py
platform/gsutil/gslib/tests/test_gcs_json_media.py
platform/gsutil/gslib/tests/test_gsutil.py
platform/gsutil/gslib/tests/test_hash.py
platform/gsutil/gslib/tests/test_hashing_helper.py
platform/gsutil/gslib/tests/test_help.py
platform/gsutil/gslib/tests/test_hmac.py
platform/gsutil/gslib/tests/test_iam.py
platform/gsutil/gslib/tests/test_kms.py
platform/gsutil/gslib/tests/test_label.py
platform/gsutil/gslib/tests/test_lifecycle.py
platform/gsutil/gslib/tests/test_logging.py
platform/gsutil/gslib/tests/test_ls.py
platform/gsutil/gslib/tests/test_mb.py
platform/gsutil/gslib/tests/test_metrics.py
platform/gsutil/gslib/tests/test_mtls.py
platform/gsutil/gslib/tests/test_mv.py
platform/gsutil/gslib/tests/test_naming.py
platform/gsutil/gslib/tests/test_notification.py
platform/gsutil/gslib/tests/test_notification_pubsub.py
platform/gsutil/gslib/tests/test_pap.py
platform/gsutil/gslib/tests/test_parallel_cp.py
platform/gsutil/gslib/tests/test_parallelism_framework.py
platform/gsutil/gslib/tests/test_perfdiag.py
platform/gsutil/gslib/tests/test_plurality_checkable_iterator.py
platform/gsutil/gslib/tests/test_posix_util.py
platform/gsutil/gslib/tests/test_psc.py
platform/gsutil/gslib/tests/test_rb.py
platform/gsutil/gslib/tests/test_requester_pays.py
platform/gsutil/gslib/tests/test_resumable_streaming.py
platform/gsutil/gslib/tests/test_retention.py
platform/gsutil/gslib/tests/test_retention_util.py
platform/gsutil/gslib/tests/test_rewrite.py
platform/gsutil/gslib/tests/test_rm.py
platform/gsutil/gslib/tests/test_rpo.py
platform/gsutil/gslib/tests/test_rsync.py
platform/gsutil/gslib/tests/test_rsync_funcs.py
platform/gsutil/gslib/tests/test_seek_ahead_thread.py
platform/gsutil/gslib/tests/test_setmeta.py
platform/gsutil/gslib/tests/test_shim_util.py
platform/gsutil/gslib/tests/test_signurl.py
platform/gsutil/gslib/tests/test_stat.py
platform/gsutil/gslib/tests/test_stet_cp.py
platform/gsutil/gslib/tests/test_stet_util.py
platform/gsutil/gslib/tests/test_storage_url.py
platform/gsutil/gslib/tests/test_tabcomplete.py
platform/gsutil/gslib/tests/test_temporary_file_util.py
platform/gsutil/gslib/tests/test_trace.py
platform/gsutil/gslib/tests/test_tracker_file.py
platform/gsutil/gslib/tests/test_ubla.py
platform/gsutil/gslib/tests/test_ui.py
platform/gsutil/gslib/tests/test_update.py
platform/gsutil/gslib/tests/test_user_agent_helper.py
platform/gsutil/gslib/tests/test_util.py
platform/gsutil/gslib/tests/test_versioning.py
platform/gsutil/gslib/tests/test_web.py
platform/gsutil/gslib/tests/test_wildcard_iterator.py
platform/gsutil/gslib/tests/test_wrapped_credentials.py
platform/gsutil/gslib/tests/testcase/__init__.py
platform/gsutil/gslib/tests/testcase/base.py
platform/gsutil/gslib/tests/testcase/integration_testcase.py
platform/gsutil/gslib/tests/testcase/shim_unit_test_base.py
platform/gsutil/gslib/tests/testcase/unit_testcase.py
platform/gsutil/gslib/tests/util.py
platform/gsutil/gslib/third_party/__init__.py
platform/gsutil/gslib/third_party/iamcredentials_apitools/__init__.py
platform/gsutil/gslib/third_party/iamcredentials_apitools/iamcredentials_v1_client.py
platform/gsutil/gslib/third_party/iamcredentials_apitools/iamcredentials_v1_messages.py
platform/gsutil/gslib/third_party/kms_apitools/__init__.py
platform/gsutil/gslib/third_party/kms_apitools/cloudkms_v1_client.py
platform/gsutil/gslib/third_party/kms_apitools/cloudkms_v1_messages.py
platform/gsutil/gslib/third_party/kms_apitools/resources.py
platform/gsutil/gslib/third_party/pubsub_apitools/__init__.py
platform/gsutil/gslib/third_party/pubsub_apitools/pubsub_v1_client.py
platform/gsutil/gslib/third_party/pubsub_apitools/pubsub_v1_messages.py
platform/gsutil/gslib/third_party/storage_apitools/__init__.py
platform/gsutil/gslib/third_party/storage_apitools/credentials_lib.py
platform/gsutil/gslib/third_party/storage_apitools/storage_v1_client.py
platform/gsutil/gslib/third_party/storage_apitools/storage_v1_messages.py
platform/gsutil/gslib/thread_message.py
platform/gsutil/gslib/tracker_file.py
platform/gsutil/gslib/tz_utc.py
platform/gsutil/gslib/ui_controller.py
platform/gsutil/gslib/utils/__init__.py
platform/gsutil/gslib/utils/acl_helper.py
platform/gsutil/gslib/utils/arg_helper.py
platform/gsutil/gslib/utils/boto_util.py
platform/gsutil/gslib/utils/cat_helper.py
platform/gsutil/gslib/utils/cloud_api_helper.py
platform/gsutil/gslib/utils/constants.py
platform/gsutil/gslib/utils/copy_helper.py
platform/gsutil/gslib/utils/encryption_helper.py
platform/gsutil/gslib/utils/execution_util.py
platform/gsutil/gslib/utils/hashing_helper.py
platform/gsutil/gslib/utils/iam_helper.py
platform/gsutil/gslib/utils/ls_helper.py
platform/gsutil/gslib/utils/metadata_util.py
platform/gsutil/gslib/utils/parallelism_framework_util.py
platform/gsutil/gslib/utils/posix_util.py
platform/gsutil/gslib/utils/retention_util.py
platform/gsutil/gslib/utils/retry_util.py
platform/gsutil/gslib/utils/rsync_util.py
platform/gsutil/gslib/utils/shim_util.py
platform/gsutil/gslib/utils/signurl_helper.py
platform/gsutil/gslib/utils/stet_util.py
platform/gsutil/gslib/utils/system_util.py
platform/gsutil/gslib/utils/temporary_file_util.py
platform/gsutil/gslib/utils/text_util.py
platform/gsutil/gslib/utils/translation_helper.py
platform/gsutil/gslib/utils/unit_util.py
platform/gsutil/gslib/utils/update_util.py
platform/gsutil/gslib/utils/user_agent_helper.py
platform/gsutil/gslib/utils/version_check.py
platform/gsutil/gslib/utils/wrapped_credentials.py
platform/gsutil/gslib/vendored/__init__.py
platform/gsutil/gslib/vendored/boto/.gitignore
platform/gsutil/gslib/vendored/boto/.travis.yml
platform/gsutil/gslib/vendored/boto/CONTRIBUTING
platform/gsutil/gslib/vendored/boto/LICENSE
platform/gsutil/gslib/vendored/boto/MANIFEST.in
platform/gsutil/gslib/vendored/boto/README.rst
platform/gsutil/gslib/vendored/boto/boto/__init__.py
platform/gsutil/gslib/vendored/boto/boto/auth.py
platform/gsutil/gslib/vendored/boto/boto/auth_handler.py
platform/gsutil/gslib/vendored/boto/boto/awslambda/__init__.py
platform/gsutil/gslib/vendored/boto/boto/awslambda/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/awslambda/layer1.py
platform/gsutil/gslib/vendored/boto/boto/beanstalk/__init__.py
platform/gsutil/gslib/vendored/boto/boto/beanstalk/exception.py
platform/gsutil/gslib/vendored/boto/boto/beanstalk/layer1.py
platform/gsutil/gslib/vendored/boto/boto/beanstalk/response.py
platform/gsutil/gslib/vendored/boto/boto/beanstalk/wrapper.py
platform/gsutil/gslib/vendored/boto/boto/cacerts/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cacerts/cacerts.txt
platform/gsutil/gslib/vendored/boto/boto/cloudformation/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudformation/connection.py
platform/gsutil/gslib/vendored/boto/boto/cloudformation/stack.py
platform/gsutil/gslib/vendored/boto/boto/cloudformation/template.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/distribution.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/exception.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/identity.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/invalidation.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/logging.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/object.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/origin.py
platform/gsutil/gslib/vendored/boto/boto/cloudfront/signers.py
platform/gsutil/gslib/vendored/boto/boto/cloudhsm/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudhsm/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/cloudhsm/layer1.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/document.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/domain.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/layer1.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/layer2.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/optionstatus.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/search.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch/sourceattribute.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/document.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/domain.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/layer1.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/layer2.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/optionstatus.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearch2/search.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearchdomain/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearchdomain/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/cloudsearchdomain/layer1.py
platform/gsutil/gslib/vendored/boto/boto/cloudtrail/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cloudtrail/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/cloudtrail/layer1.py
platform/gsutil/gslib/vendored/boto/boto/codedeploy/__init__.py
platform/gsutil/gslib/vendored/boto/boto/codedeploy/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/codedeploy/layer1.py
platform/gsutil/gslib/vendored/boto/boto/cognito/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cognito/identity/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cognito/identity/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/cognito/identity/layer1.py
platform/gsutil/gslib/vendored/boto/boto/cognito/sync/__init__.py
platform/gsutil/gslib/vendored/boto/boto/cognito/sync/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/cognito/sync/layer1.py
platform/gsutil/gslib/vendored/boto/boto/compat.py
platform/gsutil/gslib/vendored/boto/boto/configservice/__init__.py
platform/gsutil/gslib/vendored/boto/boto/configservice/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/configservice/layer1.py
platform/gsutil/gslib/vendored/boto/boto/connection.py
platform/gsutil/gslib/vendored/boto/boto/contrib/__init__.py
platform/gsutil/gslib/vendored/boto/boto/contrib/ymlmessage.py
platform/gsutil/gslib/vendored/boto/boto/datapipeline/__init__.py
platform/gsutil/gslib/vendored/boto/boto/datapipeline/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/datapipeline/layer1.py
platform/gsutil/gslib/vendored/boto/boto/directconnect/__init__.py
platform/gsutil/gslib/vendored/boto/boto/directconnect/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/directconnect/layer1.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/__init__.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/batch.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/condition.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/item.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/layer1.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/layer2.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/schema.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/table.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb/types.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/__init__.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/fields.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/items.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/layer1.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/results.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/table.py
platform/gsutil/gslib/vendored/boto/boto/dynamodb2/types.py
platform/gsutil/gslib/vendored/boto/boto/ec2/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ec2/address.py
platform/gsutil/gslib/vendored/boto/boto/ec2/attributes.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/activity.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/group.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/instance.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/launchconfig.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/limits.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/policy.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/request.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/scheduled.py
platform/gsutil/gslib/vendored/boto/boto/ec2/autoscale/tag.py
platform/gsutil/gslib/vendored/boto/boto/ec2/blockdevicemapping.py
platform/gsutil/gslib/vendored/boto/boto/ec2/bundleinstance.py
platform/gsutil/gslib/vendored/boto/boto/ec2/buyreservation.py
platform/gsutil/gslib/vendored/boto/boto/ec2/cloudwatch/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ec2/cloudwatch/alarm.py
platform/gsutil/gslib/vendored/boto/boto/ec2/cloudwatch/datapoint.py
platform/gsutil/gslib/vendored/boto/boto/ec2/cloudwatch/dimension.py
platform/gsutil/gslib/vendored/boto/boto/ec2/cloudwatch/listelement.py
platform/gsutil/gslib/vendored/boto/boto/ec2/cloudwatch/metric.py
platform/gsutil/gslib/vendored/boto/boto/ec2/connection.py
platform/gsutil/gslib/vendored/boto/boto/ec2/ec2object.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/attributes.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/healthcheck.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/instancestate.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/listelement.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/listener.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/loadbalancer.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/policies.py
platform/gsutil/gslib/vendored/boto/boto/ec2/elb/securitygroup.py
platform/gsutil/gslib/vendored/boto/boto/ec2/group.py
platform/gsutil/gslib/vendored/boto/boto/ec2/image.py
platform/gsutil/gslib/vendored/boto/boto/ec2/instance.py
platform/gsutil/gslib/vendored/boto/boto/ec2/instanceinfo.py
platform/gsutil/gslib/vendored/boto/boto/ec2/instancestatus.py
platform/gsutil/gslib/vendored/boto/boto/ec2/instancetype.py
platform/gsutil/gslib/vendored/boto/boto/ec2/keypair.py
platform/gsutil/gslib/vendored/boto/boto/ec2/launchspecification.py
platform/gsutil/gslib/vendored/boto/boto/ec2/networkinterface.py
platform/gsutil/gslib/vendored/boto/boto/ec2/placementgroup.py
platform/gsutil/gslib/vendored/boto/boto/ec2/regioninfo.py
platform/gsutil/gslib/vendored/boto/boto/ec2/reservedinstance.py
platform/gsutil/gslib/vendored/boto/boto/ec2/securitygroup.py
platform/gsutil/gslib/vendored/boto/boto/ec2/snapshot.py
platform/gsutil/gslib/vendored/boto/boto/ec2/spotdatafeedsubscription.py
platform/gsutil/gslib/vendored/boto/boto/ec2/spotinstancerequest.py
platform/gsutil/gslib/vendored/boto/boto/ec2/spotpricehistory.py
platform/gsutil/gslib/vendored/boto/boto/ec2/tag.py
platform/gsutil/gslib/vendored/boto/boto/ec2/volume.py
platform/gsutil/gslib/vendored/boto/boto/ec2/volumestatus.py
platform/gsutil/gslib/vendored/boto/boto/ec2/zone.py
platform/gsutil/gslib/vendored/boto/boto/ec2containerservice/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ec2containerservice/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/ec2containerservice/layer1.py
platform/gsutil/gslib/vendored/boto/boto/ecs/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ecs/item.py
platform/gsutil/gslib/vendored/boto/boto/elasticache/__init__.py
platform/gsutil/gslib/vendored/boto/boto/elasticache/layer1.py
platform/gsutil/gslib/vendored/boto/boto/elastictranscoder/__init__.py
platform/gsutil/gslib/vendored/boto/boto/elastictranscoder/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/elastictranscoder/layer1.py
platform/gsutil/gslib/vendored/boto/boto/emr/__init__.py
platform/gsutil/gslib/vendored/boto/boto/emr/bootstrap_action.py
platform/gsutil/gslib/vendored/boto/boto/emr/connection.py
platform/gsutil/gslib/vendored/boto/boto/emr/emrobject.py
platform/gsutil/gslib/vendored/boto/boto/emr/instance_group.py
platform/gsutil/gslib/vendored/boto/boto/emr/step.py
platform/gsutil/gslib/vendored/boto/boto/endpoints.json
platform/gsutil/gslib/vendored/boto/boto/endpoints.py
platform/gsutil/gslib/vendored/boto/boto/exception.py
platform/gsutil/gslib/vendored/boto/boto/file/README
platform/gsutil/gslib/vendored/boto/boto/file/__init__.py
platform/gsutil/gslib/vendored/boto/boto/file/bucket.py
platform/gsutil/gslib/vendored/boto/boto/file/connection.py
platform/gsutil/gslib/vendored/boto/boto/file/key.py
platform/gsutil/gslib/vendored/boto/boto/file/simpleresultset.py
platform/gsutil/gslib/vendored/boto/boto/fps/__init__.py
platform/gsutil/gslib/vendored/boto/boto/fps/connection.py
platform/gsutil/gslib/vendored/boto/boto/fps/exception.py
platform/gsutil/gslib/vendored/boto/boto/fps/response.py
platform/gsutil/gslib/vendored/boto/boto/glacier/__init__.py
platform/gsutil/gslib/vendored/boto/boto/glacier/concurrent.py
platform/gsutil/gslib/vendored/boto/boto/glacier/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/glacier/job.py
platform/gsutil/gslib/vendored/boto/boto/glacier/layer1.py
platform/gsutil/gslib/vendored/boto/boto/glacier/layer2.py
platform/gsutil/gslib/vendored/boto/boto/glacier/response.py
platform/gsutil/gslib/vendored/boto/boto/glacier/utils.py
platform/gsutil/gslib/vendored/boto/boto/glacier/vault.py
platform/gsutil/gslib/vendored/boto/boto/glacier/writer.py
platform/gsutil/gslib/vendored/boto/boto/gs/__init__.py
platform/gsutil/gslib/vendored/boto/boto/gs/acl.py
platform/gsutil/gslib/vendored/boto/boto/gs/bucket.py
platform/gsutil/gslib/vendored/boto/boto/gs/bucketlistresultset.py
platform/gsutil/gslib/vendored/boto/boto/gs/connection.py
platform/gsutil/gslib/vendored/boto/boto/gs/cors.py
platform/gsutil/gslib/vendored/boto/boto/gs/encryptionconfig.py
platform/gsutil/gslib/vendored/boto/boto/gs/key.py
platform/gsutil/gslib/vendored/boto/boto/gs/lifecycle.py
platform/gsutil/gslib/vendored/boto/boto/gs/resumable_upload_handler.py
platform/gsutil/gslib/vendored/boto/boto/gs/user.py
platform/gsutil/gslib/vendored/boto/boto/handler.py
platform/gsutil/gslib/vendored/boto/boto/https_connection.py
platform/gsutil/gslib/vendored/boto/boto/iam/__init__.py
platform/gsutil/gslib/vendored/boto/boto/iam/connection.py
platform/gsutil/gslib/vendored/boto/boto/iam/summarymap.py
platform/gsutil/gslib/vendored/boto/boto/jsonresponse.py
platform/gsutil/gslib/vendored/boto/boto/kinesis/__init__.py
platform/gsutil/gslib/vendored/boto/boto/kinesis/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/kinesis/layer1.py
platform/gsutil/gslib/vendored/boto/boto/kms/__init__.py
platform/gsutil/gslib/vendored/boto/boto/kms/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/kms/layer1.py
platform/gsutil/gslib/vendored/boto/boto/logs/__init__.py
platform/gsutil/gslib/vendored/boto/boto/logs/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/logs/layer1.py
platform/gsutil/gslib/vendored/boto/boto/machinelearning/__init__.py
platform/gsutil/gslib/vendored/boto/boto/machinelearning/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/machinelearning/layer1.py
platform/gsutil/gslib/vendored/boto/boto/manage/__init__.py
platform/gsutil/gslib/vendored/boto/boto/manage/cmdshell.py
platform/gsutil/gslib/vendored/boto/boto/manage/propget.py
platform/gsutil/gslib/vendored/boto/boto/manage/server.py
platform/gsutil/gslib/vendored/boto/boto/manage/task.py
platform/gsutil/gslib/vendored/boto/boto/manage/test_manage.py
platform/gsutil/gslib/vendored/boto/boto/manage/volume.py
platform/gsutil/gslib/vendored/boto/boto/mashups/__init__.py
platform/gsutil/gslib/vendored/boto/boto/mashups/interactive.py
platform/gsutil/gslib/vendored/boto/boto/mashups/iobject.py
platform/gsutil/gslib/vendored/boto/boto/mashups/order.py
platform/gsutil/gslib/vendored/boto/boto/mashups/server.py
platform/gsutil/gslib/vendored/boto/boto/mturk/__init__.py
platform/gsutil/gslib/vendored/boto/boto/mturk/connection.py
platform/gsutil/gslib/vendored/boto/boto/mturk/layoutparam.py
platform/gsutil/gslib/vendored/boto/boto/mturk/notification.py
platform/gsutil/gslib/vendored/boto/boto/mturk/price.py
platform/gsutil/gslib/vendored/boto/boto/mturk/qualification.py
platform/gsutil/gslib/vendored/boto/boto/mturk/question.py
platform/gsutil/gslib/vendored/boto/boto/mws/__init__.py
platform/gsutil/gslib/vendored/boto/boto/mws/connection.py
platform/gsutil/gslib/vendored/boto/boto/mws/exception.py
platform/gsutil/gslib/vendored/boto/boto/mws/response.py
platform/gsutil/gslib/vendored/boto/boto/opsworks/__init__.py
platform/gsutil/gslib/vendored/boto/boto/opsworks/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/opsworks/layer1.py
platform/gsutil/gslib/vendored/boto/boto/plugin.py
platform/gsutil/gslib/vendored/boto/boto/provider.py
platform/gsutil/gslib/vendored/boto/boto/pyami/__init__.py
platform/gsutil/gslib/vendored/boto/boto/pyami/bootstrap.py
platform/gsutil/gslib/vendored/boto/boto/pyami/config.py
platform/gsutil/gslib/vendored/boto/boto/pyami/copybot.cfg
platform/gsutil/gslib/vendored/boto/boto/pyami/copybot.py
platform/gsutil/gslib/vendored/boto/boto/pyami/helloworld.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/__init__.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/ubuntu/__init__.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/ubuntu/apache.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/ubuntu/ebs.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/ubuntu/installer.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/ubuntu/mysql.py
platform/gsutil/gslib/vendored/boto/boto/pyami/installers/ubuntu/trac.py
platform/gsutil/gslib/vendored/boto/boto/pyami/launch_ami.py
platform/gsutil/gslib/vendored/boto/boto/pyami/scriptbase.py
platform/gsutil/gslib/vendored/boto/boto/pyami/startup.py
platform/gsutil/gslib/vendored/boto/boto/rds/__init__.py
platform/gsutil/gslib/vendored/boto/boto/rds/dbinstance.py
platform/gsutil/gslib/vendored/boto/boto/rds/dbsecuritygroup.py
platform/gsutil/gslib/vendored/boto/boto/rds/dbsnapshot.py
platform/gsutil/gslib/vendored/boto/boto/rds/dbsubnetgroup.py
platform/gsutil/gslib/vendored/boto/boto/rds/event.py
platform/gsutil/gslib/vendored/boto/boto/rds/logfile.py
platform/gsutil/gslib/vendored/boto/boto/rds/optiongroup.py
platform/gsutil/gslib/vendored/boto/boto/rds/parametergroup.py
platform/gsutil/gslib/vendored/boto/boto/rds/regioninfo.py
platform/gsutil/gslib/vendored/boto/boto/rds/statusinfo.py
platform/gsutil/gslib/vendored/boto/boto/rds/vpcsecuritygroupmembership.py
platform/gsutil/gslib/vendored/boto/boto/rds2/__init__.py
platform/gsutil/gslib/vendored/boto/boto/rds2/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/rds2/layer1.py
platform/gsutil/gslib/vendored/boto/boto/redshift/__init__.py
platform/gsutil/gslib/vendored/boto/boto/redshift/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/redshift/layer1.py
platform/gsutil/gslib/vendored/boto/boto/regioninfo.py
platform/gsutil/gslib/vendored/boto/boto/requestlog.py
platform/gsutil/gslib/vendored/boto/boto/resultset.py
platform/gsutil/gslib/vendored/boto/boto/roboto/__init__.py
platform/gsutil/gslib/vendored/boto/boto/roboto/awsqueryrequest.py
platform/gsutil/gslib/vendored/boto/boto/roboto/awsqueryservice.py
platform/gsutil/gslib/vendored/boto/boto/roboto/param.py
platform/gsutil/gslib/vendored/boto/boto/route53/__init__.py
platform/gsutil/gslib/vendored/boto/boto/route53/connection.py
platform/gsutil/gslib/vendored/boto/boto/route53/domains/__init__.py
platform/gsutil/gslib/vendored/boto/boto/route53/domains/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/route53/domains/layer1.py
platform/gsutil/gslib/vendored/boto/boto/route53/exception.py
platform/gsutil/gslib/vendored/boto/boto/route53/healthcheck.py
platform/gsutil/gslib/vendored/boto/boto/route53/hostedzone.py
platform/gsutil/gslib/vendored/boto/boto/route53/record.py
platform/gsutil/gslib/vendored/boto/boto/route53/status.py
platform/gsutil/gslib/vendored/boto/boto/route53/zone.py
platform/gsutil/gslib/vendored/boto/boto/s3/__init__.py
platform/gsutil/gslib/vendored/boto/boto/s3/acl.py
platform/gsutil/gslib/vendored/boto/boto/s3/bucket.py
platform/gsutil/gslib/vendored/boto/boto/s3/bucketlistresultset.py
platform/gsutil/gslib/vendored/boto/boto/s3/bucketlogging.py
platform/gsutil/gslib/vendored/boto/boto/s3/connection.py
platform/gsutil/gslib/vendored/boto/boto/s3/cors.py
platform/gsutil/gslib/vendored/boto/boto/s3/deletemarker.py
platform/gsutil/gslib/vendored/boto/boto/s3/key.py
platform/gsutil/gslib/vendored/boto/boto/s3/keyfile.py
platform/gsutil/gslib/vendored/boto/boto/s3/lifecycle.py
platform/gsutil/gslib/vendored/boto/boto/s3/multidelete.py
platform/gsutil/gslib/vendored/boto/boto/s3/multipart.py
platform/gsutil/gslib/vendored/boto/boto/s3/prefix.py
platform/gsutil/gslib/vendored/boto/boto/s3/resumable_download_handler.py
platform/gsutil/gslib/vendored/boto/boto/s3/tagging.py
platform/gsutil/gslib/vendored/boto/boto/s3/user.py
platform/gsutil/gslib/vendored/boto/boto/s3/website.py
platform/gsutil/gslib/vendored/boto/boto/sdb/__init__.py
platform/gsutil/gslib/vendored/boto/boto/sdb/connection.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/__init__.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/blob.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/key.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/manager/__init__.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/manager/sdbmanager.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/manager/xmlmanager.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/model.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/property.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/query.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/sequence.py
platform/gsutil/gslib/vendored/boto/boto/sdb/db/test_db.py
platform/gsutil/gslib/vendored/boto/boto/sdb/domain.py
platform/gsutil/gslib/vendored/boto/boto/sdb/item.py
platform/gsutil/gslib/vendored/boto/boto/sdb/queryresultset.py
platform/gsutil/gslib/vendored/boto/boto/sdb/regioninfo.py
platform/gsutil/gslib/vendored/boto/boto/services/__init__.py
platform/gsutil/gslib/vendored/boto/boto/services/bs.py
platform/gsutil/gslib/vendored/boto/boto/services/message.py
platform/gsutil/gslib/vendored/boto/boto/services/result.py
platform/gsutil/gslib/vendored/boto/boto/services/service.py
platform/gsutil/gslib/vendored/boto/boto/services/servicedef.py
platform/gsutil/gslib/vendored/boto/boto/services/sonofmmm.cfg
platform/gsutil/gslib/vendored/boto/boto/services/sonofmmm.py
platform/gsutil/gslib/vendored/boto/boto/services/submit.py
platform/gsutil/gslib/vendored/boto/boto/ses/__init__.py
platform/gsutil/gslib/vendored/boto/boto/ses/connection.py
platform/gsutil/gslib/vendored/boto/boto/ses/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/sns/__init__.py
platform/gsutil/gslib/vendored/boto/boto/sns/connection.py
platform/gsutil/gslib/vendored/boto/boto/sqs/__init__.py
platform/gsutil/gslib/vendored/boto/boto/sqs/attributes.py
platform/gsutil/gslib/vendored/boto/boto/sqs/batchresults.py
platform/gsutil/gslib/vendored/boto/boto/sqs/bigmessage.py
platform/gsutil/gslib/vendored/boto/boto/sqs/connection.py
platform/gsutil/gslib/vendored/boto/boto/sqs/jsonmessage.py
platform/gsutil/gslib/vendored/boto/boto/sqs/message.py
platform/gsutil/gslib/vendored/boto/boto/sqs/messageattributes.py
platform/gsutil/gslib/vendored/boto/boto/sqs/queue.py
platform/gsutil/gslib/vendored/boto/boto/sqs/regioninfo.py
platform/gsutil/gslib/vendored/boto/boto/storage_uri.py
platform/gsutil/gslib/vendored/boto/boto/sts/__init__.py
platform/gsutil/gslib/vendored/boto/boto/sts/connection.py
platform/gsutil/gslib/vendored/boto/boto/sts/credentials.py
platform/gsutil/gslib/vendored/boto/boto/support/__init__.py
platform/gsutil/gslib/vendored/boto/boto/support/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/support/layer1.py
platform/gsutil/gslib/vendored/boto/boto/swf/__init__.py
platform/gsutil/gslib/vendored/boto/boto/swf/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/swf/layer1.py
platform/gsutil/gslib/vendored/boto/boto/swf/layer1_decisions.py
platform/gsutil/gslib/vendored/boto/boto/swf/layer2.py
platform/gsutil/gslib/vendored/boto/boto/utils.py
platform/gsutil/gslib/vendored/boto/boto/vendored/__init__.py
platform/gsutil/gslib/vendored/boto/boto/vendored/regions/__init__.py
platform/gsutil/gslib/vendored/boto/boto/vendored/regions/exceptions.py
platform/gsutil/gslib/vendored/boto/boto/vendored/regions/regions.py
platform/gsutil/gslib/vendored/boto/boto/vendored/six.py
platform/gsutil/gslib/vendored/boto/boto/vpc/__init__.py
platform/gsutil/gslib/vendored/boto/boto/vpc/customergateway.py
platform/gsutil/gslib/vendored/boto/boto/vpc/dhcpoptions.py
platform/gsutil/gslib/vendored/boto/boto/vpc/internetgateway.py
platform/gsutil/gslib/vendored/boto/boto/vpc/networkacl.py
platform/gsutil/gslib/vendored/boto/boto/vpc/routetable.py
platform/gsutil/gslib/vendored/boto/boto/vpc/subnet.py
platform/gsutil/gslib/vendored/boto/boto/vpc/vpc.py
platform/gsutil/gslib/vendored/boto/boto/vpc/vpc_peering_connection.py
platform/gsutil/gslib/vendored/boto/boto/vpc/vpnconnection.py
platform/gsutil/gslib/vendored/boto/boto/vpc/vpngateway.py
platform/gsutil/gslib/vendored/boto/pylintrc
platform/gsutil/gslib/vendored/boto/requirements-docs.txt
platform/gsutil/gslib/vendored/boto/requirements-py33.txt
platform/gsutil/gslib/vendored/boto/requirements.txt
platform/gsutil/gslib/vendored/boto/setup.cfg
platform/gsutil/gslib/vendored/boto/setup.py
platform/gsutil/gslib/vendored/boto/tests/__init__.py
platform/gsutil/gslib/vendored/boto/tests/compat.py
platform/gsutil/gslib/vendored/boto/tests/db/test_lists.py
platform/gsutil/gslib/vendored/boto/tests/db/test_password.py
platform/gsutil/gslib/vendored/boto/tests/db/test_query.py
platform/gsutil/gslib/vendored/boto/tests/db/test_sequence.py
platform/gsutil/gslib/vendored/boto/tests/devpay/__init__.py
platform/gsutil/gslib/vendored/boto/tests/devpay/test_s3.py
platform/gsutil/gslib/vendored/boto/tests/fps/__init__.py
platform/gsutil/gslib/vendored/boto/tests/fps/test.py
platform/gsutil/gslib/vendored/boto/tests/fps/test_verify_signature.py
platform/gsutil/gslib/vendored/boto/tests/integration/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/awslambda/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/awslambda/test_awslambda.py
platform/gsutil/gslib/vendored/boto/tests/integration/beanstalk/test_wrapper.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudformation/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudformation/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudformation/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudhsm/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudhsm/test_cloudhsm.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudsearch/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudsearch/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudsearch/test_layers.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudsearch2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudsearch2/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudsearch2/test_layers.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudtrail/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudtrail/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/cloudtrail/test_cloudtrail.py
platform/gsutil/gslib/vendored/boto/tests/integration/codedeploy/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/codedeploy/test_codedeploy.py
platform/gsutil/gslib/vendored/boto/tests/integration/cognito/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cognito/identity/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cognito/identity/test_cognito_identity.py
platform/gsutil/gslib/vendored/boto/tests/integration/cognito/sync/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/cognito/sync/test_cognito_sync.py
platform/gsutil/gslib/vendored/boto/tests/integration/configservice/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/configservice/test_configservice.py
platform/gsutil/gslib/vendored/boto/tests/integration/datapipeline/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/datapipeline/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/datapipeline/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/directconnect/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/directconnect/test_directconnect.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb/test_layer2.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb/test_table.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb2/forum_test_data.json
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb2/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb2/test_highlevel.py
platform/gsutil/gslib/vendored/boto/tests/integration/dynamodb2/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/autoscale/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/autoscale/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/autoscale/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/cloudwatch/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/cloudwatch/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/cloudwatch/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/elb/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/elb/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/elb/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/vpc/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2/vpc/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2containerservice/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ec2containerservice/test_ec2containerservice.py
platform/gsutil/gslib/vendored/boto/tests/integration/elasticache/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/elasticache/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/elastictranscoder/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/elastictranscoder/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/elastictranscoder/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/emr/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/emr/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/glacier/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/glacier/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/glacier/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/glacier/test_layer2.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/cb_test_harness.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/test_basic.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/test_generation_conditionals.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/test_resumable_downloads.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/test_resumable_uploads.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/test_storage_uri.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/test_versioning.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/testcase.py
platform/gsutil/gslib/vendored/boto/tests/integration/gs/util.py
platform/gsutil/gslib/vendored/boto/tests/integration/iam/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/iam/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/iam/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/iam/test_password_policy.py
platform/gsutil/gslib/vendored/boto/tests/integration/iam/test_policy.py
platform/gsutil/gslib/vendored/boto/tests/integration/kinesis/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/kinesis/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/kinesis/test_kinesis.py
platform/gsutil/gslib/vendored/boto/tests/integration/kms/test_kms.py
platform/gsutil/gslib/vendored/boto/tests/integration/logs/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/logs/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/logs/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/mws/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/mws/test.py
platform/gsutil/gslib/vendored/boto/tests/integration/opsworks/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/opsworks/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds/test_db_subnet_group.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds/test_promote_modify.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds2/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/rds2/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/redshift/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/redshift/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/redshift/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/domains/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/domains/test_route53domains.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/test_alias_resourcerecordsets.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/test_health_check.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/test_resourcerecordsets.py
platform/gsutil/gslib/vendored/boto/tests/integration/route53/test_zone.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/mock_storage_service.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/other_cacerts.txt
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_bucket.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_connect_to_region.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_cors.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_encryption.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_https_cert_validation.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_key.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_mfa.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_multidelete.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_multipart.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_pool.py
platform/gsutil/gslib/vendored/boto/tests/integration/s3/test_versioning.py
platform/gsutil/gslib/vendored/boto/tests/integration/sdb/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/sdb/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/sdb/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/ses/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/ses/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/ses/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/sns/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/sns/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/sns/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/sns/test_sns_sqs_subscription.py
platform/gsutil/gslib/vendored/boto/tests/integration/sqs/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/sqs/test_bigmessage.py
platform/gsutil/gslib/vendored/boto/tests/integration/sqs/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/sqs/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/integration/storage_uri/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/storage_uri/test_storage_uri.py
platform/gsutil/gslib/vendored/boto/tests/integration/sts/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/sts/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/sts/test_session_token.py
platform/gsutil/gslib/vendored/boto/tests/integration/support/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/support/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/support/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/swf/__init__.py
platform/gsutil/gslib/vendored/boto/tests/integration/swf/test_cert_verification.py
platform/gsutil/gslib/vendored/boto/tests/integration/swf/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/integration/swf/test_layer1_workflow_execution.py
platform/gsutil/gslib/vendored/boto/tests/mturk/.gitignore
platform/gsutil/gslib/vendored/boto/tests/mturk/__init__.py
platform/gsutil/gslib/vendored/boto/tests/mturk/_init_environment.py
platform/gsutil/gslib/vendored/boto/tests/mturk/all_tests.py
platform/gsutil/gslib/vendored/boto/tests/mturk/cleanup_tests.py
platform/gsutil/gslib/vendored/boto/tests/mturk/common.py
platform/gsutil/gslib/vendored/boto/tests/mturk/create_free_text_question_regex.doctest
platform/gsutil/gslib/vendored/boto/tests/mturk/create_hit.doctest
platform/gsutil/gslib/vendored/boto/tests/mturk/create_hit_binary.doctest
platform/gsutil/gslib/vendored/boto/tests/mturk/create_hit_external.py
platform/gsutil/gslib/vendored/boto/tests/mturk/create_hit_from_hit_type.doctest
platform/gsutil/gslib/vendored/boto/tests/mturk/create_hit_test.py
platform/gsutil/gslib/vendored/boto/tests/mturk/create_hit_with_qualifications.py
platform/gsutil/gslib/vendored/boto/tests/mturk/hit_persistence.py
platform/gsutil/gslib/vendored/boto/tests/mturk/mocks.py
platform/gsutil/gslib/vendored/boto/tests/mturk/reviewable_hits.doctest
platform/gsutil/gslib/vendored/boto/tests/mturk/run-doctest.py
platform/gsutil/gslib/vendored/boto/tests/mturk/search_hits.doctest
platform/gsutil/gslib/vendored/boto/tests/mturk/selenium_support.py
platform/gsutil/gslib/vendored/boto/tests/mturk/support.py
platform/gsutil/gslib/vendored/boto/tests/mturk/test_disable_hit.py
platform/gsutil/gslib/vendored/boto/tests/test.py
platform/gsutil/gslib/vendored/boto/tests/unit/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/auth/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/auth/test_sigv4.py
platform/gsutil/gslib/vendored/boto/tests/unit/auth/test_stsanon.py
platform/gsutil/gslib/vendored/boto/tests/unit/awslambda/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/awslambda/test_awslambda.py
platform/gsutil/gslib/vendored/boto/tests/unit/beanstalk/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/beanstalk/test_exception.py
platform/gsutil/gslib/vendored/boto/tests/unit/beanstalk/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudformation/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudformation/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudformation/test_stack.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudfront/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudfront/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudfront/test_distribution.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudfront/test_invalidation.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudfront/test_invalidation_list.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudfront/test_signed_urls.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch/test_document.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch/test_exceptions.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch/test_search.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch2/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch2/test_document.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch2/test_exceptions.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearch2/test_search.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearchdomain/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudsearchdomain/test_cloudsearchdomain.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudtrail/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/cloudtrail/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/unit/data/new_endpoints.json
platform/gsutil/gslib/vendored/boto/tests/unit/data/old_endpoints.json
platform/gsutil/gslib/vendored/boto/tests/unit/directconnect/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/directconnect/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb/test_batch.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb/test_layer2.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb/test_types.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb2/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/unit/dynamodb2/test_table.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/autoscale/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/autoscale/test_group.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/cloudwatch/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/cloudwatch/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/elb/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/elb/test_attribute.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/elb/test_listener.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/elb/test_loadbalancer.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_address.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_blockdevicemapping.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_ec2object.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_instance.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_instancestatus.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_instancetype.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_networkinterface.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_reservedinstance.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_securitygroup.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_snapshot.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_spotinstance.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2/test_volume.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2containerservice/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ec2containerservice/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/ecs/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ecs/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/elasticache/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/elasticache/test_api_interface.py
platform/gsutil/gslib/vendored/boto/tests/unit/emr/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/emr/test_emr_responses.py
platform/gsutil/gslib/vendored/boto/tests/unit/emr/test_instance_group_args.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_concurrent.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_job.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_layer2.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_response.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_utils.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_vault.py
platform/gsutil/gslib/vendored/boto/tests/unit/glacier/test_writer.py
platform/gsutil/gslib/vendored/boto/tests/unit/iam/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/iam/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/iam/test_policy.py
platform/gsutil/gslib/vendored/boto/tests/unit/kinesis/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/kinesis/test_kinesis.py
platform/gsutil/gslib/vendored/boto/tests/unit/kms/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/kms/test_kms.py
platform/gsutil/gslib/vendored/boto/tests/unit/logs/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/logs/test_layer1.py
platform/gsutil/gslib/vendored/boto/tests/unit/machinelearning/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/machinelearning/test_machinelearning.py
platform/gsutil/gslib/vendored/boto/tests/unit/manage/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/manage/test_ssh.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/test_locale_qualification_in.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/test_locale_qualification_notin.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/test_qualification_doesnotexist.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/test_qualification_exists.py
platform/gsutil/gslib/vendored/boto/tests/unit/mturk/test_qualification_qualtypewithscore_in.py
platform/gsutil/gslib/vendored/boto/tests/unit/mws/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/mws/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/mws/test_response.py
platform/gsutil/gslib/vendored/boto/tests/unit/provider/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/provider/test_provider.py
platform/gsutil/gslib/vendored/boto/tests/unit/pyami/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/pyami/test_config.py
platform/gsutil/gslib/vendored/boto/tests/unit/rds/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/rds/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/rds/test_snapshot.py
platform/gsutil/gslib/vendored/boto/tests/unit/rds2/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/rds2/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/route53/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/route53/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/route53/test_zone.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_bucket.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_bucketlistresultset.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_cors_configuration.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_key.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_keyfile.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_lifecycle.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_tagging.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_uri.py
platform/gsutil/gslib/vendored/boto/tests/unit/s3/test_website.py
platform/gsutil/gslib/vendored/boto/tests/unit/ses/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/ses/test_identity.py
platform/gsutil/gslib/vendored/boto/tests/unit/sns/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/sns/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/sqs/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/sqs/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/sqs/test_message.py
platform/gsutil/gslib/vendored/boto/tests/unit/sqs/test_queue.py
platform/gsutil/gslib/vendored/boto/tests/unit/sts/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/sts/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/sts/test_credentials.py
platform/gsutil/gslib/vendored/boto/tests/unit/swf/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/swf/test_layer1_decisions.py
platform/gsutil/gslib/vendored/boto/tests/unit/swf/test_layer2_actors.py
platform/gsutil/gslib/vendored/boto/tests/unit/swf/test_layer2_base.py
platform/gsutil/gslib/vendored/boto/tests/unit/swf/test_layer2_domain.py
platform/gsutil/gslib/vendored/boto/tests/unit/swf/test_layer2_types.py
platform/gsutil/gslib/vendored/boto/tests/unit/test_connect_to_region.py
platform/gsutil/gslib/vendored/boto/tests/unit/test_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/test_endpoints.json
platform/gsutil/gslib/vendored/boto/tests/unit/test_endpoints.py
platform/gsutil/gslib/vendored/boto/tests/unit/test_exception.py
platform/gsutil/gslib/vendored/boto/tests/unit/test_regioninfo.py
platform/gsutil/gslib/vendored/boto/tests/unit/utils/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/utils/test_utils.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/__init__.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_customergateway.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_dhcpoptions.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_internetgateway.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_networkacl.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_routetable.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_subnet.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_vpc.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_vpc_peering_connection.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_vpnconnection.py
platform/gsutil/gslib/vendored/boto/tests/unit/vpc/test_vpngateway.py
platform/gsutil/gslib/vendored/boto/tox.ini
platform/gsutil/gslib/vendored/oauth2client/.coveragerc
platform/gsutil/gslib/vendored/oauth2client/.github/ISSUE_TEMPLATE.md
platform/gsutil/gslib/vendored/oauth2client/.github/PULL_REQUEST_TEMPLATE.md
platform/gsutil/gslib/vendored/oauth2client/.gitignore
platform/gsutil/gslib/vendored/oauth2client/.travis.yml
platform/gsutil/gslib/vendored/oauth2client/CHANGELOG.md
platform/gsutil/gslib/vendored/oauth2client/CODE_OF_CONDUCT.md
platform/gsutil/gslib/vendored/oauth2client/CONTRIBUTING.md
platform/gsutil/gslib/vendored/oauth2client/CONTRIBUTORS.md
platform/gsutil/gslib/vendored/oauth2client/LICENSE
platform/gsutil/gslib/vendored/oauth2client/MANIFEST.in
platform/gsutil/gslib/vendored/oauth2client/Makefile
platform/gsutil/gslib/vendored/oauth2client/README.md
platform/gsutil/gslib/vendored/oauth2client/docs/Makefile
platform/gsutil/gslib/vendored/oauth2client/docs/_static/favicon.ico
platform/gsutil/gslib/vendored/oauth2client/docs/_static/google_logo.png
platform/gsutil/gslib/vendored/oauth2client/docs/conf.py
platform/gsutil/gslib/vendored/oauth2client/docs/index.rst
platform/gsutil/gslib/vendored/oauth2client/docs/requirements.txt
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.client.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.clientsecrets.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.appengine.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.devshell.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.dictionary_storage.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.apps.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.decorators.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.models.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.signals.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.site.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.storage.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.django_util.views.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.flask_util.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.gce.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.keyring_storage.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.multiprocess_file_storage.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.sqlalchemy.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.contrib.xsrfutil.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.crypt.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.file.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.service_account.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.tools.rst
platform/gsutil/gslib/vendored/oauth2client/docs/source/oauth2client.transport.rst
platform/gsutil/gslib/vendored/oauth2client/oauth2client/__init__.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/_helpers.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/_openssl_crypt.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/_pkce.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/_pure_python_crypt.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/_pycrypto_crypt.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/client.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/clientsecrets.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/__init__.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/_appengine_ndb.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/_metadata.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/appengine.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/devshell.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/dictionary_storage.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/__init__.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/apps.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/decorators.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/models.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/signals.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/site.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/storage.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/django_util/views.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/flask_util.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/gce.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/keyring_storage.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/multiprocess_file_storage.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/sqlalchemy.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/contrib/xsrfutil.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/crypt.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/file.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/service_account.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/tools.py
platform/gsutil/gslib/vendored/oauth2client/oauth2client/transport.py
platform/gsutil/gslib/vendored/oauth2client/samples/call_compute_service.py
platform/gsutil/gslib/vendored/oauth2client/samples/googleappengine/app.yaml
platform/gsutil/gslib/vendored/oauth2client/samples/googleappengine/call_compute_service_from_gae.py
platform/gsutil/gslib/vendored/oauth2client/samples/oauth2_for_devices.py
platform/gsutil/gslib/vendored/oauth2client/scripts/build_docs.sh
platform/gsutil/gslib/vendored/oauth2client/scripts/install.sh
platform/gsutil/gslib/vendored/oauth2client/scripts/local_test_setup.sample
platform/gsutil/gslib/vendored/oauth2client/scripts/run.sh
platform/gsutil/gslib/vendored/oauth2client/scripts/run_gce_system_tests.py
platform/gsutil/gslib/vendored/oauth2client/scripts/run_system_tests.py
platform/gsutil/gslib/vendored/oauth2client/scripts/run_system_tests.sh
platform/gsutil/gslib/vendored/oauth2client/setup.cfg
platform/gsutil/gslib/vendored/oauth2client/setup.py
platform/gsutil/gslib/vendored/oauth2client/tox.ini
platform/gsutil/gslib/wildcard_iterator.py
platform/gsutil/gsutil
platform/gsutil/gsutil.py
platform/gsutil/setup.py
platform/gsutil/third_party/apitools/.coveragerc
platform/gsutil/third_party/apitools/.gitignore
platform/gsutil/third_party/apitools/.travis.yml
platform/gsutil/third_party/apitools/LICENSE
platform/gsutil/third_party/apitools/MANIFEST.in
platform/gsutil/third_party/apitools/README.rst
platform/gsutil/third_party/apitools/apitools/__init__.py
platform/gsutil/third_party/apitools/apitools/base/__init__.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/__init__.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/descriptor.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/descriptor_test.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/message_types.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/message_types_test.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/messages.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/messages_test.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/protojson.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/protojson_test.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/test_util.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/util.py
platform/gsutil/third_party/apitools/apitools/base/protorpclite/util_test.py
platform/gsutil/third_party/apitools/apitools/base/py/__init__.py
platform/gsutil/third_party/apitools/apitools/base/py/base_api.py
platform/gsutil/third_party/apitools/apitools/base/py/base_api_test.py
platform/gsutil/third_party/apitools/apitools/base/py/batch.py
platform/gsutil/third_party/apitools/apitools/base/py/batch_test.py
platform/gsutil/third_party/apitools/apitools/base/py/buffered_stream.py
platform/gsutil/third_party/apitools/apitools/base/py/buffered_stream_test.py
platform/gsutil/third_party/apitools/apitools/base/py/compression.py
platform/gsutil/third_party/apitools/apitools/base/py/compression_test.py
platform/gsutil/third_party/apitools/apitools/base/py/credentials_lib.py
platform/gsutil/third_party/apitools/apitools/base/py/credentials_lib_test.py
platform/gsutil/third_party/apitools/apitools/base/py/encoding.py
platform/gsutil/third_party/apitools/apitools/base/py/encoding_helper.py
platform/gsutil/third_party/apitools/apitools/base/py/encoding_test.py
platform/gsutil/third_party/apitools/apitools/base/py/exceptions.py
platform/gsutil/third_party/apitools/apitools/base/py/exceptions_test.py
platform/gsutil/third_party/apitools/apitools/base/py/extra_types.py
platform/gsutil/third_party/apitools/apitools/base/py/extra_types_test.py
platform/gsutil/third_party/apitools/apitools/base/py/gzip.py
platform/gsutil/third_party/apitools/apitools/base/py/gzip_test.py
platform/gsutil/third_party/apitools/apitools/base/py/http_wrapper.py
platform/gsutil/third_party/apitools/apitools/base/py/http_wrapper_test.py
platform/gsutil/third_party/apitools/apitools/base/py/list_pager.py
platform/gsutil/third_party/apitools/apitools/base/py/list_pager_test.py
platform/gsutil/third_party/apitools/apitools/base/py/stream_slice.py
platform/gsutil/third_party/apitools/apitools/base/py/stream_slice_test.py
platform/gsutil/third_party/apitools/apitools/base/py/testing/__init__.py
platform/gsutil/third_party/apitools/apitools/base/py/testing/mock.py
platform/gsutil/third_party/apitools/apitools/base/py/testing/mock_test.py
platform/gsutil/third_party/apitools/apitools/base/py/transfer.py
platform/gsutil/third_party/apitools/apitools/base/py/transfer_test.py
platform/gsutil/third_party/apitools/apitools/base/py/util.py
platform/gsutil/third_party/apitools/apitools/base/py/util_test.py
platform/gsutil/third_party/apitools/apitools/gen/__init__.py
platform/gsutil/third_party/apitools/apitools/gen/client_generation_test.py
platform/gsutil/third_party/apitools/apitools/gen/extended_descriptor.py
platform/gsutil/third_party/apitools/apitools/gen/gen_client.py
platform/gsutil/third_party/apitools/apitools/gen/gen_client_lib.py
platform/gsutil/third_party/apitools/apitools/gen/gen_client_test.py
platform/gsutil/third_party/apitools/apitools/gen/message_registry.py
platform/gsutil/third_party/apitools/apitools/gen/service_registry.py
platform/gsutil/third_party/apitools/apitools/gen/test_utils.py
platform/gsutil/third_party/apitools/apitools/gen/testdata/compute/compute_2025-01-01-preview.json
platform/gsutil/third_party/apitools/apitools/gen/testdata/dns/dns_2015-08-07-preview.json
platform/gsutil/third_party/apitools/apitools/gen/testdata/dns/dns_v1.json
platform/gsutil/third_party/apitools/apitools/gen/util.py
platform/gsutil/third_party/apitools/apitools/gen/util_test.py
platform/gsutil/third_party/apitools/apitools/scripts/__init__.py
platform/gsutil/third_party/apitools/apitools/scripts/testdata/fake_client_secrets.json
platform/gsutil/third_party/apitools/apitools/scripts/testdata/noninstalled_client_secrets.json
platform/gsutil/third_party/apitools/ez_setup.py
platform/gsutil/third_party/apitools/samples/__init__.py
platform/gsutil/third_party/apitools/samples/bigquery_sample/bigquery_v2.json
platform/gsutil/third_party/apitools/samples/bigquery_sample/bigquery_v2/__init__.py
platform/gsutil/third_party/apitools/samples/bigquery_sample/bigquery_v2/bigquery_v2_client.py
platform/gsutil/third_party/apitools/samples/bigquery_sample/bigquery_v2/bigquery_v2_messages.py
platform/gsutil/third_party/apitools/samples/dns_sample/__init__.py
platform/gsutil/third_party/apitools/samples/dns_sample/dns_v1.json
platform/gsutil/third_party/apitools/samples/dns_sample/dns_v1/__init__.py
platform/gsutil/third_party/apitools/samples/dns_sample/dns_v1/dns_v1_client.py
platform/gsutil/third_party/apitools/samples/dns_sample/dns_v1/dns_v1_messages.py
platform/gsutil/third_party/apitools/samples/dns_sample/gen_dns_client_test.py
platform/gsutil/third_party/apitools/samples/fusiontables_sample/__init__.py
platform/gsutil/third_party/apitools/samples/fusiontables_sample/fusiontables_v1.json
platform/gsutil/third_party/apitools/samples/fusiontables_sample/fusiontables_v1/__init__.py
platform/gsutil/third_party/apitools/samples/fusiontables_sample/fusiontables_v1/fusiontables_v1_client.py
platform/gsutil/third_party/apitools/samples/fusiontables_sample/fusiontables_v1/fusiontables_v1_messages.py
platform/gsutil/third_party/apitools/samples/iam_sample/__init__.py
platform/gsutil/third_party/apitools/samples/iam_sample/iam_client_test.py
platform/gsutil/third_party/apitools/samples/iam_sample/iam_v1.json
platform/gsutil/third_party/apitools/samples/iam_sample/iam_v1/__init__.py
platform/gsutil/third_party/apitools/samples/iam_sample/iam_v1/iam_v1_client.py
platform/gsutil/third_party/apitools/samples/iam_sample/iam_v1/iam_v1_messages.py
platform/gsutil/third_party/apitools/samples/regenerate_samples.py
platform/gsutil/third_party/apitools/samples/servicemanagement_sample/__init__.py
platform/gsutil/third_party/apitools/samples/servicemanagement_sample/messages_test.py
platform/gsutil/third_party/apitools/samples/servicemanagement_sample/servicemanagement_v1.json
platform/gsutil/third_party/apitools/samples/servicemanagement_sample/servicemanagement_v1/__init__.py
platform/gsutil/third_party/apitools/samples/servicemanagement_sample/servicemanagement_v1/servicemanagement_v1_client.py
platform/gsutil/third_party/apitools/samples/servicemanagement_sample/servicemanagement_v1/servicemanagement_v1_messages.py
platform/gsutil/third_party/apitools/samples/storage_sample/downloads_test.py
platform/gsutil/third_party/apitools/samples/storage_sample/generate_clients.sh
platform/gsutil/third_party/apitools/samples/storage_sample/storage_v1.json
platform/gsutil/third_party/apitools/samples/storage_sample/storage_v1/__init__.py
platform/gsutil/third_party/apitools/samples/storage_sample/storage_v1/storage_v1_client.py
platform/gsutil/third_party/apitools/samples/storage_sample/storage_v1/storage_v1_messages.py
platform/gsutil/third_party/apitools/samples/storage_sample/testdata/fifteen_byte_file
platform/gsutil/third_party/apitools/samples/storage_sample/testdata/filename_with_spaces
platform/gsutil/third_party/apitools/samples/storage_sample/uploads_test.py
platform/gsutil/third_party/apitools/samples/uptodate_check_test.py
platform/gsutil/third_party/apitools/setup.cfg
platform/gsutil/third_party/apitools/setup.py
platform/gsutil/third_party/apitools/tox.ini
platform/gsutil/third_party/argcomplete/.github/FUNDING.yml
platform/gsutil/third_party/argcomplete/.github/workflows/ci.yml
platform/gsutil/third_party/argcomplete/.github/workflows/release.yml
platform/gsutil/third_party/argcomplete/.gitignore
platform/gsutil/third_party/argcomplete/Authors.rst
platform/gsutil/third_party/argcomplete/Changes.rst
platform/gsutil/third_party/argcomplete/LICENSE.rst
platform/gsutil/third_party/argcomplete/MANIFEST.in
platform/gsutil/third_party/argcomplete/Makefile
platform/gsutil/third_party/argcomplete/NOTICE
platform/gsutil/third_party/argcomplete/README.rst
platform/gsutil/third_party/argcomplete/SECURITY.md
platform/gsutil/third_party/argcomplete/argcomplete/__init__.py
platform/gsutil/third_party/argcomplete/argcomplete/_check_console_script.py
platform/gsutil/third_party/argcomplete/argcomplete/_check_module.py
platform/gsutil/third_party/argcomplete/argcomplete/bash_completion.d/_python-argcomplete
platform/gsutil/third_party/argcomplete/argcomplete/completers.py
platform/gsutil/third_party/argcomplete/argcomplete/exceptions.py
platform/gsutil/third_party/argcomplete/argcomplete/finders.py
platform/gsutil/third_party/argcomplete/argcomplete/io.py
platform/gsutil/third_party/argcomplete/argcomplete/lexers.py
platform/gsutil/third_party/argcomplete/argcomplete/packages/__init__.py
platform/gsutil/third_party/argcomplete/argcomplete/packages/_argparse.py
platform/gsutil/third_party/argcomplete/argcomplete/packages/_shlex.py
platform/gsutil/third_party/argcomplete/argcomplete/py.typed
platform/gsutil/third_party/argcomplete/argcomplete/scripts/__init__.py
platform/gsutil/third_party/argcomplete/argcomplete/scripts/activate_global_python_argcomplete.py
platform/gsutil/third_party/argcomplete/argcomplete/scripts/python_argcomplete_check_easy_install_script.py
platform/gsutil/third_party/argcomplete/argcomplete/scripts/register_python_argcomplete.py
platform/gsutil/third_party/argcomplete/argcomplete/shell_integration.py
platform/gsutil/third_party/argcomplete/common.mk
platform/gsutil/third_party/argcomplete/contrib/README.rst
platform/gsutil/third_party/argcomplete/docs/changelog.rst
platform/gsutil/third_party/argcomplete/docs/conf.py
platform/gsutil/third_party/argcomplete/docs/examples/describe_github_user.py
platform/gsutil/third_party/argcomplete/docs/fish_help_string.png
platform/gsutil/third_party/argcomplete/docs/index.rst
platform/gsutil/third_party/argcomplete/docs/toc.html
platform/gsutil/third_party/argcomplete/pyproject.toml
platform/gsutil/third_party/argcomplete/setup.cfg
platform/gsutil/third_party/argcomplete/test/__init__.py
platform/gsutil/third_party/argcomplete/test/inputrc
platform/gsutil/third_party/argcomplete/test/prog
platform/gsutil/third_party/argcomplete/test/stuck
platform/gsutil/third_party/argcomplete/test/test.py
platform/gsutil/third_party/argcomplete/test/test_contrib_shells.py
platform/gsutil/third_party/argcomplete/test/test_package/__init__.py
platform/gsutil/third_party/argcomplete/test/test_package/setup.py
platform/gsutil/third_party/argcomplete/test/test_package/test_module.py
platform/gsutil/third_party/argcomplete/test/test_package/test_package/__init__.py
platform/gsutil/third_party/cachetools/.github/FUNDING.yml
platform/gsutil/third_party/cachetools/.github/ISSUE_TEMPLATE/bug_report.md
platform/gsutil/third_party/cachetools/.github/ISSUE_TEMPLATE/config.yml
platform/gsutil/third_party/cachetools/.github/ISSUE_TEMPLATE/feature_request.md
platform/gsutil/third_party/cachetools/.github/SECURITY.md
platform/gsutil/third_party/cachetools/.github/dependabot.yml
platform/gsutil/third_party/cachetools/.github/workflows/ci.yml
platform/gsutil/third_party/cachetools/.gitignore
platform/gsutil/third_party/cachetools/.readthedocs.yaml
platform/gsutil/third_party/cachetools/CHANGELOG.rst
platform/gsutil/third_party/cachetools/LICENSE
platform/gsutil/third_party/cachetools/MANIFEST.in
platform/gsutil/third_party/cachetools/README.rst
platform/gsutil/third_party/cachetools/docs/conf.py
platform/gsutil/third_party/cachetools/docs/index.rst
platform/gsutil/third_party/cachetools/pyproject.toml
platform/gsutil/third_party/cachetools/setup.cfg
platform/gsutil/third_party/cachetools/setup.py
platform/gsutil/third_party/cachetools/src/cachetools/__init__.py
platform/gsutil/third_party/cachetools/src/cachetools/func.py
platform/gsutil/third_party/cachetools/src/cachetools/keys.py
platform/gsutil/third_party/cachetools/tests/__init__.py
platform/gsutil/third_party/cachetools/tests/test_cache.py
platform/gsutil/third_party/cachetools/tests/test_cached.py
platform/gsutil/third_party/cachetools/tests/test_cachedmethod.py
platform/gsutil/third_party/cachetools/tests/test_fifo.py
platform/gsutil/third_party/cachetools/tests/test_func.py
platform/gsutil/third_party/cachetools/tests/test_keys.py
platform/gsutil/third_party/cachetools/tests/test_lfu.py
platform/gsutil/third_party/cachetools/tests/test_lru.py
platform/gsutil/third_party/cachetools/tests/test_mru.py
platform/gsutil/third_party/cachetools/tests/test_rr.py
platform/gsutil/third_party/cachetools/tests/test_tlru.py
platform/gsutil/third_party/cachetools/tests/test_ttl.py
platform/gsutil/third_party/cachetools/tox.ini
platform/gsutil/third_party/certifi/.github/dependabot.yml
platform/gsutil/third_party/certifi/.github/workflows/bump.yml
platform/gsutil/third_party/certifi/.github/workflows/ci.yml
platform/gsutil/third_party/certifi/.github/workflows/lock.yml
platform/gsutil/third_party/certifi/.github/workflows/release.yml
platform/gsutil/third_party/certifi/.gitignore
platform/gsutil/third_party/certifi/LICENSE
platform/gsutil/third_party/certifi/MANIFEST.in
platform/gsutil/third_party/certifi/Makefile
platform/gsutil/third_party/certifi/README.rst
platform/gsutil/third_party/certifi/SECURITY.md
platform/gsutil/third_party/certifi/certifi/__init__.py
platform/gsutil/third_party/certifi/certifi/__main__.py
platform/gsutil/third_party/certifi/certifi/cacert.pem
platform/gsutil/third_party/certifi/certifi/core.py
platform/gsutil/third_party/certifi/certifi/py.typed
platform/gsutil/third_party/certifi/certifi/tests/__init__.py
platform/gsutil/third_party/certifi/certifi/tests/test_certify.py
platform/gsutil/third_party/certifi/pyproject.toml
platform/gsutil/third_party/certifi/setup.py
platform/gsutil/third_party/certifi/strip-non-ascii
platform/gsutil/third_party/chardet/.git-blame-ignore-revs
platform/gsutil/third_party/chardet/.gitattributes
platform/gsutil/third_party/chardet/.github/workflows/test.yml
platform/gsutil/third_party/chardet/.gitignore
platform/gsutil/third_party/chardet/.pre-commit-config.yaml
platform/gsutil/third_party/chardet/.prospector.yaml
platform/gsutil/third_party/chardet/LICENSE
platform/gsutil/third_party/chardet/MANIFEST.in
platform/gsutil/third_party/chardet/NOTES.rst
platform/gsutil/third_party/chardet/README.rst
platform/gsutil/third_party/chardet/bench.py
platform/gsutil/third_party/chardet/chardet/__init__.py
platform/gsutil/third_party/chardet/chardet/__main__.py
platform/gsutil/third_party/chardet/chardet/big5freq.py
platform/gsutil/third_party/chardet/chardet/big5prober.py
platform/gsutil/third_party/chardet/chardet/chardistribution.py
platform/gsutil/third_party/chardet/chardet/charsetgroupprober.py
platform/gsutil/third_party/chardet/chardet/charsetprober.py
platform/gsutil/third_party/chardet/chardet/cli/__init__.py
platform/gsutil/third_party/chardet/chardet/cli/chardetect.py
platform/gsutil/third_party/chardet/chardet/codingstatemachine.py
platform/gsutil/third_party/chardet/chardet/codingstatemachinedict.py
platform/gsutil/third_party/chardet/chardet/cp949prober.py
platform/gsutil/third_party/chardet/chardet/enums.py
platform/gsutil/third_party/chardet/chardet/escprober.py
platform/gsutil/third_party/chardet/chardet/escsm.py
platform/gsutil/third_party/chardet/chardet/eucjpprober.py
platform/gsutil/third_party/chardet/chardet/euckrfreq.py
platform/gsutil/third_party/chardet/chardet/euckrprober.py
platform/gsutil/third_party/chardet/chardet/euctwfreq.py
platform/gsutil/third_party/chardet/chardet/euctwprober.py
platform/gsutil/third_party/chardet/chardet/gb2312freq.py
platform/gsutil/third_party/chardet/chardet/gb2312prober.py
platform/gsutil/third_party/chardet/chardet/hebrewprober.py
platform/gsutil/third_party/chardet/chardet/jisfreq.py
platform/gsutil/third_party/chardet/chardet/johabfreq.py
platform/gsutil/third_party/chardet/chardet/johabprober.py
platform/gsutil/third_party/chardet/chardet/jpcntx.py
platform/gsutil/third_party/chardet/chardet/langbulgarianmodel.py
platform/gsutil/third_party/chardet/chardet/langgreekmodel.py
platform/gsutil/third_party/chardet/chardet/langhebrewmodel.py
platform/gsutil/third_party/chardet/chardet/langhungarianmodel.py
platform/gsutil/third_party/chardet/chardet/langrussianmodel.py
platform/gsutil/third_party/chardet/chardet/langthaimodel.py
platform/gsutil/third_party/chardet/chardet/langturkishmodel.py
platform/gsutil/third_party/chardet/chardet/latin1prober.py
platform/gsutil/third_party/chardet/chardet/macromanprober.py
platform/gsutil/third_party/chardet/chardet/mbcharsetprober.py
platform/gsutil/third_party/chardet/chardet/mbcsgroupprober.py
platform/gsutil/third_party/chardet/chardet/mbcssm.py
platform/gsutil/third_party/chardet/chardet/metadata/__init__.py
platform/gsutil/third_party/chardet/chardet/metadata/languages.py
platform/gsutil/third_party/chardet/chardet/py.typed
platform/gsutil/third_party/chardet/chardet/resultdict.py
platform/gsutil/third_party/chardet/chardet/sbcharsetprober.py
platform/gsutil/third_party/chardet/chardet/sbcsgroupprober.py
platform/gsutil/third_party/chardet/chardet/sjisprober.py
platform/gsutil/third_party/chardet/chardet/universaldetector.py
platform/gsutil/third_party/chardet/chardet/utf1632prober.py
platform/gsutil/third_party/chardet/chardet/utf8prober.py
platform/gsutil/third_party/chardet/chardet/version.py
platform/gsutil/third_party/chardet/convert_language_model.py
platform/gsutil/third_party/chardet/docs/.gitignore
platform/gsutil/third_party/chardet/docs/Makefile
platform/gsutil/third_party/chardet/docs/README.md
platform/gsutil/third_party/chardet/docs/api/chardet.rst
platform/gsutil/third_party/chardet/docs/api/modules.rst
platform/gsutil/third_party/chardet/docs/conf.py
platform/gsutil/third_party/chardet/docs/faq.rst
platform/gsutil/third_party/chardet/docs/how-it-works.rst
platform/gsutil/third_party/chardet/docs/index.rst
platform/gsutil/third_party/chardet/docs/make.bat
platform/gsutil/third_party/chardet/docs/supported-encodings.rst
platform/gsutil/third_party/chardet/docs/usage.rst
platform/gsutil/third_party/chardet/pyproject.toml
platform/gsutil/third_party/chardet/setup.cfg
platform/gsutil/third_party/chardet/test.py
platform/gsutil/third_party/chardet/tests/Big5/0804.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/_chromium_Big5_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/Big5/_ude_1.txt
platform/gsutil/third_party/chardet/tests/Big5/blog.worren.net.xml
platform/gsutil/third_party/chardet/tests/Big5/carbonxiv.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/catshadow.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/coolloud.org.tw.xml
platform/gsutil/third_party/chardet/tests/Big5/digitalwall.com.xml
platform/gsutil/third_party/chardet/tests/Big5/ebao.us.xml
platform/gsutil/third_party/chardet/tests/Big5/fudesign.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/kafkatseng.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/ke207.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/leavesth.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/letterlego.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/linyijen.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/marilynwu.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/myblog.pchome.com.tw.xml
platform/gsutil/third_party/chardet/tests/Big5/oui-design.com.xml
platform/gsutil/third_party/chardet/tests/Big5/sanwenji.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/sinica.edu.tw.xml
platform/gsutil/third_party/chardet/tests/Big5/sylvia1976.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/tlkkuo.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/unoriginalblog.com.xml
platform/gsutil/third_party/chardet/tests/Big5/upsaid.com.xml
platform/gsutil/third_party/chardet/tests/Big5/willythecop.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/Big5/ytc.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/CP932/hardsoft.at.webry.info.xml
platform/gsutil/third_party/chardet/tests/CP932/www2.chuo-u.ac.jp-suishin.xml
platform/gsutil/third_party/chardet/tests/CP932/y-moto.com.xml
platform/gsutil/third_party/chardet/tests/CP949/ricanet.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/_mozilla_bug426271_text-euc-jp.html
platform/gsutil/third_party/chardet/tests/EUC-JP/_mozilla_bug431054_text.html
platform/gsutil/third_party/chardet/tests/EUC-JP/_mozilla_bug620106_text.html
platform/gsutil/third_party/chardet/tests/EUC-JP/_ude_1.txt
platform/gsutil/third_party/chardet/tests/EUC-JP/aivy.co.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/akaname.main.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/arclamp.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/aristrist.s57.xrea.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/artifact-jp.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/atom.ycf.nanet.co.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/azito.under.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/azoz.org.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/blog.kabu-navi.com.atom.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/blog.kabu-navi.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/bphrs.net.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/ch.kitaguni.tv.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/club.h14m.org.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/contents-factory.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/furusatonoeki.cutegirl.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/manana.moo.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/mimizun.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/misuzilla.org.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/overcube.com.atom.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/overcube.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/pinkupa.com.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/rdf.ycf.nanet.co.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/siesta.co.jp.aozora.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/tls.org.xml
platform/gsutil/third_party/chardet/tests/EUC-JP/yukiboh.moo.jp.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/_chromium_windows-949_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/EUC-KR/_mozilla_bug9357_text.html
platform/gsutil/third_party/chardet/tests/EUC-KR/_ude_euc1.txt
platform/gsutil/third_party/chardet/tests/EUC-KR/_ude_euc2.txt
platform/gsutil/third_party/chardet/tests/EUC-KR/acnnewswire.net.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/alogblog.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/arts.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/birder.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/blog.bd-lab.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/blog.empas.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/blog.rss.naver.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/calmguy.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/chisato.info.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/console.linuxstudy.pe.kr.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/critique.or.kr.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/epitaph.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/ittrend.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/jely.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/jely.pe.kr.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/jowchung.oolim.net.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/kina.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/lennon81.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/oroll.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/poliplus.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/scarletkh2.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/siwoo.org.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/sparcs.kaist.ac.kr.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/tori02.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/willis.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/xenix.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/yunho.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-KR/zangsalang.egloos.com.xml
platform/gsutil/third_party/chardet/tests/EUC-TW/_ude_euc-tw1.txt
platform/gsutil/third_party/chardet/tests/GB2312/14.blog.westca.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/2.blog.westca.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/_chromium_gb18030_with_no_encoding_specified.html.xml
platform/gsutil/third_party/chardet/tests/GB2312/_mozilla_bug171813_text.html
platform/gsutil/third_party/chardet/tests/GB2312/acnnewswire.net.xml
platform/gsutil/third_party/chardet/tests/GB2312/bbs.blogsome.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/cappuccinos.3322.org.xml
platform/gsutil/third_party/chardet/tests/GB2312/chen56.blogcn.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/cindychen.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/cnblog.org.xml
platform/gsutil/third_party/chardet/tests/GB2312/coverer.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/eighthday.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/godthink.blogsome.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/jjgod.3322.org.xml
platform/gsutil/third_party/chardet/tests/GB2312/lily.blogsome.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/luciferwang.blogcn.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/pda.blogsome.com.xml
platform/gsutil/third_party/chardet/tests/GB2312/softsea.net.xml
platform/gsutil/third_party/chardet/tests/GB2312/w3cn.org.xml
platform/gsutil/third_party/chardet/tests/GB2312/xy15400.blogcn.com.xml
platform/gsutil/third_party/chardet/tests/IBM855/_ude_1.txt
platform/gsutil/third_party/chardet/tests/IBM855/aif.ru.health.xml
platform/gsutil/third_party/chardet/tests/IBM855/aug32.hole.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/aviaport.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/blog.mlmaster.com.xml
platform/gsutil/third_party/chardet/tests/IBM855/forum.template-toolkit.ru.1.xml
platform/gsutil/third_party/chardet/tests/IBM855/forum.template-toolkit.ru.4.xml
platform/gsutil/third_party/chardet/tests/IBM855/forum.template-toolkit.ru.6.xml
platform/gsutil/third_party/chardet/tests/IBM855/forum.template-toolkit.ru.8.xml
platform/gsutil/third_party/chardet/tests/IBM855/forum.template-toolkit.ru.9.xml
platform/gsutil/third_party/chardet/tests/IBM855/greek.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/intertat.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/janulalife.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/IBM855/kapranoff.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/money.rin.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/music.peeps.ru.xml
platform/gsutil/third_party/chardet/tests/IBM855/newsru.com.xml
platform/gsutil/third_party/chardet/tests/IBM855/susu.ac.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/_ude_1.txt
platform/gsutil/third_party/chardet/tests/IBM866/aif.ru.health.xml
platform/gsutil/third_party/chardet/tests/IBM866/aug32.hole.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/aviaport.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/blog.mlmaster.com.xml
platform/gsutil/third_party/chardet/tests/IBM866/forum.template-toolkit.ru.1.xml
platform/gsutil/third_party/chardet/tests/IBM866/forum.template-toolkit.ru.4.xml
platform/gsutil/third_party/chardet/tests/IBM866/forum.template-toolkit.ru.6.xml
platform/gsutil/third_party/chardet/tests/IBM866/forum.template-toolkit.ru.8.xml
platform/gsutil/third_party/chardet/tests/IBM866/forum.template-toolkit.ru.9.xml
platform/gsutil/third_party/chardet/tests/IBM866/greek.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/intertat.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/janulalife.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/IBM866/kapranoff.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/money.rin.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/music.peeps.ru.xml
platform/gsutil/third_party/chardet/tests/IBM866/newsru.com.xml
platform/gsutil/third_party/chardet/tests/IBM866/susu.ac.ru.xml
platform/gsutil/third_party/chardet/tests/Johab/hlpro-readme.txt
platform/gsutil/third_party/chardet/tests/Johab/iyagi-readme.txt
platform/gsutil/third_party/chardet/tests/Johab/mdir-doc.txt
platform/gsutil/third_party/chardet/tests/KOI8-R/_chromium_KOI8-R_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/KOI8-R/_ude_1.txt
platform/gsutil/third_party/chardet/tests/KOI8-R/aif.ru.health.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/aug32.hole.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/aviaport.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/blog.mlmaster.com.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/forum.template-toolkit.ru.1.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/forum.template-toolkit.ru.4.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/forum.template-toolkit.ru.6.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/forum.template-toolkit.ru.8.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/forum.template-toolkit.ru.9.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/greek.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/intertat.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/janulalife.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/kapranoff.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/koi.kinder.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/money.rin.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/music.peeps.ru.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/newsru.com.xml
platform/gsutil/third_party/chardet/tests/KOI8-R/susu.ac.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/_ude_1.txt
platform/gsutil/third_party/chardet/tests/MacCyrillic/aif.ru.health.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/aug32.hole.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/aviaport.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/blog.mlmaster.com.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/forum.template-toolkit.ru.4.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/forum.template-toolkit.ru.6.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/forum.template-toolkit.ru.8.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/forum.template-toolkit.ru.9.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/greek.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/intertat.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/kapranoff.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/koi.kinder.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/money.rin.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/music.peeps.ru.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/newsru.com.xml
platform/gsutil/third_party/chardet/tests/MacCyrillic/susu.ac.ru.xml
platform/gsutil/third_party/chardet/tests/MacRoman/ioreg_output.txt
platform/gsutil/third_party/chardet/tests/README.txt
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/10e.org.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/1affliate.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/_chromium_Shift-JIS_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/_ude_1.txt
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/_ude_2.txt
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/_ude_3.txt
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/_ude_4.txt
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/accessories-brand.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/amefoot.net.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/andore.com.inami.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/andore.com.money.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/andore.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/blog.inkase.net.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/blog.paseri.ne.jp.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/bloglelife.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/brag.zaka.to.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/celeb.lalalu.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/clickablewords.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/do.beginnersrack.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/dogsinn.jp.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/grebeweb.net.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/milliontimes.jp.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/moon-light.ne.jp.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/nextbeaut.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/ooganemochi.com.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/perth-on.net.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/sakusaka-silk.net.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/setsuzei119.jp.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/tamuyou.haun.org.xml
platform/gsutil/third_party/chardet/tests/SHIFT_JIS/yasuhisa.com.xml
platform/gsutil/third_party/chardet/tests/TIS-620/_mozilla_bug488426_text.html
platform/gsutil/third_party/chardet/tests/TIS-620/opentle.org.xml
platform/gsutil/third_party/chardet/tests/TIS-620/pharmacy.kku.ac.th.analyse1.xml
platform/gsutil/third_party/chardet/tests/TIS-620/pharmacy.kku.ac.th.centerlab.xml
platform/gsutil/third_party/chardet/tests/TIS-620/pharmacy.kku.ac.th.healthinfo-ne.xml
platform/gsutil/third_party/chardet/tests/TIS-620/trickspot.boxchart.com.xml
platform/gsutil/third_party/chardet/tests/UTF-16/bom-utf-16-be.srt
platform/gsutil/third_party/chardet/tests/UTF-16/bom-utf-16-le.srt
platform/gsutil/third_party/chardet/tests/UTF-16BE/nobom-utf16be.txt
platform/gsutil/third_party/chardet/tests/UTF-16BE/plane1-utf-16be.html
platform/gsutil/third_party/chardet/tests/UTF-16LE/nobom-utf16le.txt
platform/gsutil/third_party/chardet/tests/UTF-16LE/plane1-utf-16le.html
platform/gsutil/third_party/chardet/tests/UTF-32/bom-utf-32-be.srt
platform/gsutil/third_party/chardet/tests/UTF-32/bom-utf-32-le.srt
platform/gsutil/third_party/chardet/tests/UTF-32BE/nobom-utf32be.txt
platform/gsutil/third_party/chardet/tests/UTF-32BE/plane1-utf-32be.html
platform/gsutil/third_party/chardet/tests/UTF-32LE/nobom-utf32le.txt
platform/gsutil/third_party/chardet/tests/UTF-32LE/plane1-utf-32le.html
platform/gsutil/third_party/chardet/tests/ascii/_chromium_iso-8859-1_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/ascii/_mozilla_bug638318_text.html
platform/gsutil/third_party/chardet/tests/ascii/howto.diveintomark.org.xml
platform/gsutil/third_party/chardet/tests/iso-2022-jp/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-2022-kr/_ude_iso1.txt
platform/gsutil/third_party/chardet/tests/iso-2022-kr/_ude_iso2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-1/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-1/_ude_2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-1/_ude_3.txt
platform/gsutil/third_party/chardet/tests/iso-8859-1/_ude_4.txt
platform/gsutil/third_party/chardet/tests/iso-8859-1/_ude_5.txt
platform/gsutil/third_party/chardet/tests/iso-8859-1/_ude_6.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-croatian/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-czech/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-czech/_ude_2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/_ude_2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/_ude_3.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/auto-apro.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/cigartower.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/escience.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/hirtv.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/honositomuhely.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/saraspatak.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/shamalt.uw.hu.mk.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/shamalt.uw.hu.mr.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/shamalt.uw.hu.mv.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/shamalt.uw.hu.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-hungarian/ugyanmar.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-2-polish/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-slovak/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-slovak/_ude_2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-2-slovene/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/aero-bg.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/bbc.co.uk.popshow.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/bpm.cult.bg.2.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/bpm.cult.bg.4.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/bpm.cult.bg.9.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/bpm.cult.bg.medusa.4.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/bpm.cult.bg.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/debian.gabrovo.com.news.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/debian.gabrovo.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/doncho.net.comments.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/ecloga.cult.bg.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/ide.li.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-bulgarian/linux-bg.org.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/_chromium_ISO-8859-5_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/aif.ru.health.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/aug32.hole.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/aviaport.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/blog.mlmaster.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/forum.template-toolkit.ru.1.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/forum.template-toolkit.ru.4.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/forum.template-toolkit.ru.6.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/forum.template-toolkit.ru.8.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/forum.template-toolkit.ru.9.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/greek.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/intertat.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/janulalife.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/kapranoff.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/money.rin.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/music.peeps.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/newsru.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-5-russian/susu.ac.ru.xml
platform/gsutil/third_party/chardet/tests/iso-8859-6-arabic/_chromium_ISO-8859-6_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/_chromium_ISO-8859-7_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/_ude_2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/_ude_3.txt
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/_ude_greek.txt
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/disabled.gr.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/hotstation.gr.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.bus.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.cmm.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.fin.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.mrk.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.mrt.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.spo.xml
platform/gsutil/third_party/chardet/tests/iso-8859-7-greek/naftemporiki.gr.wld.xml
platform/gsutil/third_party/chardet/tests/iso-8859-9-turkish/_ude_1.txt
platform/gsutil/third_party/chardet/tests/iso-8859-9-turkish/_ude_2.txt
platform/gsutil/third_party/chardet/tests/iso-8859-9-turkish/divxplanet.com.xml
platform/gsutil/third_party/chardet/tests/iso-8859-9-turkish/subtitle.srt
platform/gsutil/third_party/chardet/tests/iso-8859-9-turkish/wikitop_tr_ISO-8859-9.txt
platform/gsutil/third_party/chardet/tests/utf-8-sig/_ude_4.txt
platform/gsutil/third_party/chardet/tests/utf-8-sig/bom-utf-8.srt
platform/gsutil/third_party/chardet/tests/utf-8/_chromium_UTF-8_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/utf-8/_mozilla_bug306272_text.html
platform/gsutil/third_party/chardet/tests/utf-8/_mozilla_bug426271_text-utf-8.html
platform/gsutil/third_party/chardet/tests/utf-8/_ude_1.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_2.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_3.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_5.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_greek.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_he1.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_he2.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_he3.txt
platform/gsutil/third_party/chardet/tests/utf-8/_ude_russian.txt
platform/gsutil/third_party/chardet/tests/utf-8/anitabee.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/utf-8/balatonblog.typepad.com.xml
platform/gsutil/third_party/chardet/tests/utf-8/boobooo.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/utf-8/linuxbox.hu.xml
platform/gsutil/third_party/chardet/tests/utf-8/pihgy.hu.xml
platform/gsutil/third_party/chardet/tests/utf-8/weblabor.hu.2.xml
platform/gsutil/third_party/chardet/tests/utf-8/weblabor.hu.xml
platform/gsutil/third_party/chardet/tests/windows-1250-croatian/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1250-czech/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1250-czech/_ude_2.txt
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/_ude_2.txt
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/_ude_3.txt
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/bbc.co.uk.hu.forum.xml
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/bbc.co.uk.hu.learningenglish.xml
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/bbc.co.uk.hu.pressreview.xml
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/bbc.co.uk.hu.xml
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/objektivhir.hu.xml
platform/gsutil/third_party/chardet/tests/windows-1250-hungarian/torokorszag.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/windows-1250-polish/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1250-romanian/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1250-slovak/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1250-slovak/_ude_2.txt
platform/gsutil/third_party/chardet/tests/windows-1250-slovak/_ude_3.txt
platform/gsutil/third_party/chardet/tests/windows-1250-slovene/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bbc.co.uk.popshow.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bpm.cult.bg.2.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bpm.cult.bg.3.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bpm.cult.bg.4.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bpm.cult.bg.9.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bpm.cult.bg.medusa.4.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/bpm.cult.bg.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/debian.gabrovo.com.news.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/debian.gabrovo.com.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/doncho.net.comments.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/doncho.net.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/ecloga.cult.bg.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/ide.li.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/informator.org.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/linux-bg.org.xml
platform/gsutil/third_party/chardet/tests/windows-1251-bulgarian/rinennor.org.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/_chromium_windows-1251_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/windows-1251-russian/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1251-russian/aif.ru.health.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/anthropology.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/aug32.hole.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/aviaport.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/blog.mlmaster.com.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/forum.template-toolkit.ru.1.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/forum.template-toolkit.ru.4.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/forum.template-toolkit.ru.6.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/forum.template-toolkit.ru.8.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/forum.template-toolkit.ru.9.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/greek.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/intertat.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/janulalife.blogspot.com.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/kapranoff.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/money.rin.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/music.peeps.ru.xml
platform/gsutil/third_party/chardet/tests/windows-1251-russian/newsru.com.xml
platform/gsutil/third_party/chardet/tests/windows-1252/_mozilla_bug421271_text.html
platform/gsutil/third_party/chardet/tests/windows-1252/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1252/_ude_2.txt
platform/gsutil/third_party/chardet/tests/windows-1252/github_bug_9.txt
platform/gsutil/third_party/chardet/tests/windows-1254-turkish/_chromium_windows-1254_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/windows-1254-turkish/_ude_1.txt
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/_chromium_ISO-8859-8_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/_chromium_windows-1255_with_no_encoding_specified.html
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/_ude_he1.txt
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/_ude_he2.txt
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/_ude_he3.txt
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/carshops.co.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/exego.net.2.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/hagada.org.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/halemo.net.edoar.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/hevra.org.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/hydepark.hevre.co.il.7957.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/info.org.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/infomed.co.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/law.co.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/maakav.org.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/neviim.net.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/notes.co.il.50.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/notes.co.il.6.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/notes.co.il.7.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/notes.co.il.8.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/pcplus.co.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/sharks.co.il.xml
platform/gsutil/third_party/chardet/tests/windows-1255-hebrew/whatsup.org.il.xml
platform/gsutil/third_party/chardet/tests/windows-1256-arabic/_chromium_windows-1256_with_no_encoding_specified.html
platform/gsutil/third_party/charset_normalizer/.codecov.yml
platform/gsutil/third_party/charset_normalizer/.coveragerc
platform/gsutil/third_party/charset_normalizer/.github/FUNDING.yml
platform/gsutil/third_party/charset_normalizer/.github/ISSUE_TEMPLATE/bug_report.md
platform/gsutil/third_party/charset_normalizer/.github/ISSUE_TEMPLATE/feature_request.md
platform/gsutil/third_party/charset_normalizer/.github/ISSUE_TEMPLATE/wrong_charset.md
platform/gsutil/third_party/charset_normalizer/.github/dependabot.yml
platform/gsutil/third_party/charset_normalizer/.github/workflows/cd.yml
platform/gsutil/third_party/charset_normalizer/.github/workflows/ci.yml
platform/gsutil/third_party/charset_normalizer/.github/workflows/codeql.yml
platform/gsutil/third_party/charset_normalizer/.github/workflows/scorecards.yml
platform/gsutil/third_party/charset_normalizer/.gitignore
platform/gsutil/third_party/charset_normalizer/.readthedocs.yaml
platform/gsutil/third_party/charset_normalizer/CHANGELOG.md
platform/gsutil/third_party/charset_normalizer/CODE_OF_CONDUCT.md
platform/gsutil/third_party/charset_normalizer/CONTRIBUTING.md
platform/gsutil/third_party/charset_normalizer/LICENSE
platform/gsutil/third_party/charset_normalizer/MANIFEST.in
platform/gsutil/third_party/charset_normalizer/README.md
platform/gsutil/third_party/charset_normalizer/SECURITY.md
platform/gsutil/third_party/charset_normalizer/UPGRADE.md
platform/gsutil/third_party/charset_normalizer/bin/bc.py
platform/gsutil/third_party/charset_normalizer/bin/coverage.py
platform/gsutil/third_party/charset_normalizer/bin/integration.py
platform/gsutil/third_party/charset_normalizer/bin/performance.py
platform/gsutil/third_party/charset_normalizer/bin/run_autofix.sh
platform/gsutil/third_party/charset_normalizer/bin/run_checks.sh
platform/gsutil/third_party/charset_normalizer/bin/serve.py
platform/gsutil/third_party/charset_normalizer/build-requirements.txt
platform/gsutil/third_party/charset_normalizer/charset_normalizer/__init__.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/__main__.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/api.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/cd.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/cli/__init__.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/cli/__main__.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/constant.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/legacy.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/md.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/models.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/py.typed
platform/gsutil/third_party/charset_normalizer/charset_normalizer/utils.py
platform/gsutil/third_party/charset_normalizer/charset_normalizer/version.py
platform/gsutil/third_party/charset_normalizer/data/NOTICE.md
platform/gsutil/third_party/charset_normalizer/data/sample-arabic-1.txt
platform/gsutil/third_party/charset_normalizer/data/sample-arabic.txt
platform/gsutil/third_party/charset_normalizer/data/sample-bulgarian.txt
platform/gsutil/third_party/charset_normalizer/data/sample-chinese.txt
platform/gsutil/third_party/charset_normalizer/data/sample-english.bom.txt
platform/gsutil/third_party/charset_normalizer/data/sample-french-1.txt
platform/gsutil/third_party/charset_normalizer/data/sample-french.txt
platform/gsutil/third_party/charset_normalizer/data/sample-greek-2.txt
platform/gsutil/third_party/charset_normalizer/data/sample-greek.txt
platform/gsutil/third_party/charset_normalizer/data/sample-hebrew-2.txt
platform/gsutil/third_party/charset_normalizer/data/sample-hebrew-3.txt
platform/gsutil/third_party/charset_normalizer/data/sample-korean.txt
platform/gsutil/third_party/charset_normalizer/data/sample-polish.txt
platform/gsutil/third_party/charset_normalizer/data/sample-russian-2.txt
platform/gsutil/third_party/charset_normalizer/data/sample-russian-3.txt
platform/gsutil/third_party/charset_normalizer/data/sample-russian.txt
platform/gsutil/third_party/charset_normalizer/data/sample-spanish.txt
platform/gsutil/third_party/charset_normalizer/data/sample-turkish.txt
platform/gsutil/third_party/charset_normalizer/dev-requirements.txt
platform/gsutil/third_party/charset_normalizer/docs/Makefile
platform/gsutil/third_party/charset_normalizer/docs/api.rst
platform/gsutil/third_party/charset_normalizer/docs/community/faq.rst
platform/gsutil/third_party/charset_normalizer/docs/community/featured.rst
platform/gsutil/third_party/charset_normalizer/docs/community/speedup.rst
platform/gsutil/third_party/charset_normalizer/docs/community/why_migrate.rst
platform/gsutil/third_party/charset_normalizer/docs/conf.py
platform/gsutil/third_party/charset_normalizer/docs/index.rst
platform/gsutil/third_party/charset_normalizer/docs/make.bat
platform/gsutil/third_party/charset_normalizer/docs/requirements.txt
platform/gsutil/third_party/charset_normalizer/docs/user/advanced_search.rst
platform/gsutil/third_party/charset_normalizer/docs/user/cli.rst
platform/gsutil/third_party/charset_normalizer/docs/user/getstarted.rst
platform/gsutil/third_party/charset_normalizer/docs/user/handling_result.rst
platform/gsutil/third_party/charset_normalizer/docs/user/miscellaneous.rst
platform/gsutil/third_party/charset_normalizer/docs/user/support.rst
platform/gsutil/third_party/charset_normalizer/setup.cfg
platform/gsutil/third_party/charset_normalizer/setup.py
platform/gsutil/third_party/charset_normalizer/tests/__init__.py
platform/gsutil/third_party/charset_normalizer/tests/test_base_detection.py
platform/gsutil/third_party/charset_normalizer/tests/test_cli.py
platform/gsutil/third_party/charset_normalizer/tests/test_coherence_detection.py
platform/gsutil/third_party/charset_normalizer/tests/test_detect_legacy.py
platform/gsutil/third_party/charset_normalizer/tests/test_edge_case.py
platform/gsutil/third_party/charset_normalizer/tests/test_full_detection.py
platform/gsutil/third_party/charset_normalizer/tests/test_isbinary.py
platform/gsutil/third_party/charset_normalizer/tests/test_large_payload.py
platform/gsutil/third_party/charset_normalizer/tests/test_logging.py
platform/gsutil/third_party/charset_normalizer/tests/test_mess_detection.py
platform/gsutil/third_party/charset_normalizer/tests/test_preemptive_detection.py
platform/gsutil/third_party/charset_normalizer/tests/test_utils.py
platform/gsutil/third_party/crcmod/.hgeol
platform/gsutil/third_party/crcmod/.hgignore
platform/gsutil/third_party/crcmod/.hgtags
platform/gsutil/third_party/crcmod/LICENSE
platform/gsutil/third_party/crcmod/MANIFEST.in
platform/gsutil/third_party/crcmod/README
platform/gsutil/third_party/crcmod/changelog
platform/gsutil/third_party/crcmod/docs/source/Makefile
platform/gsutil/third_party/crcmod/docs/source/conf.py
platform/gsutil/third_party/crcmod/docs/source/crcmod.predefined.rst
platform/gsutil/third_party/crcmod/docs/source/crcmod.rst
platform/gsutil/third_party/crcmod/docs/source/index.rst
platform/gsutil/third_party/crcmod/docs/source/intro.rst
platform/gsutil/third_party/crcmod/docs/source/make.bat
platform/gsutil/third_party/crcmod/docs/source/make_predefined_table.py
platform/gsutil/third_party/crcmod/python2/crcmod/__init__.py
platform/gsutil/third_party/crcmod/python2/crcmod/_crcfunpy.py
platform/gsutil/third_party/crcmod/python2/crcmod/crcmod.py
platform/gsutil/third_party/crcmod/python2/crcmod/predefined.py
platform/gsutil/third_party/crcmod/python2/crcmod/test.py
platform/gsutil/third_party/crcmod/python2/src/_crcfunext.c
platform/gsutil/third_party/crcmod/python3/crcmod/__init__.py
platform/gsutil/third_party/crcmod/python3/crcmod/_crcfunpy.py
platform/gsutil/third_party/crcmod/python3/crcmod/crcmod.py
platform/gsutil/third_party/crcmod/python3/crcmod/predefined.py
platform/gsutil/third_party/crcmod/python3/crcmod/test.py
platform/gsutil/third_party/crcmod/python3/src/_crcfunext.c
platform/gsutil/third_party/crcmod/setup.py
platform/gsutil/third_party/crcmod/test/examples.py
platform/gsutil/third_party/crcmod/test/test_crcmod.py
platform/gsutil/third_party/crcmod_osx/LICENSE
platform/gsutil/third_party/crcmod_osx/README.md
platform/gsutil/third_party/crcmod_osx/crcmod/__init__.py
platform/gsutil/third_party/crcmod_osx/crcmod/_crcfunext.so
platform/gsutil/third_party/crcmod_osx/crcmod/_crcfunpy.py
platform/gsutil/third_party/crcmod_osx/crcmod/crcmod.py
platform/gsutil/third_party/crcmod_osx/crcmod/predefined.py
platform/gsutil/third_party/crcmod_osx/crcmod/test.py
platform/gsutil/third_party/fasteners/.travis.yml
platform/gsutil/third_party/fasteners/ChangeLog
platform/gsutil/third_party/fasteners/LICENSE
platform/gsutil/third_party/fasteners/MANIFEST.in
platform/gsutil/third_party/fasteners/README.rst
platform/gsutil/third_party/fasteners/doc/source/api/lock.rst
platform/gsutil/third_party/fasteners/doc/source/api/process_lock.rst
platform/gsutil/third_party/fasteners/doc/source/conf.py
platform/gsutil/third_party/fasteners/doc/source/img/safety-pin-small.png
platform/gsutil/third_party/fasteners/doc/source/index.rst
platform/gsutil/third_party/fasteners/fasteners/__init__.py
platform/gsutil/third_party/fasteners/fasteners/_utils.py
platform/gsutil/third_party/fasteners/fasteners/lock.py
platform/gsutil/third_party/fasteners/fasteners/process_lock.py
platform/gsutil/third_party/fasteners/fasteners/test.py
platform/gsutil/third_party/fasteners/fasteners/tests/__init__.py
platform/gsutil/third_party/fasteners/fasteners/tests/test_decorators.py
platform/gsutil/third_party/fasteners/fasteners/tests/test_helpers.py
platform/gsutil/third_party/fasteners/fasteners/tests/test_lock.py
platform/gsutil/third_party/fasteners/fasteners/tests/test_process_lock.py
platform/gsutil/third_party/fasteners/fasteners/version.py
platform/gsutil/third_party/fasteners/setup.cfg
platform/gsutil/third_party/fasteners/setup.py
platform/gsutil/third_party/fasteners/test-requirements.txt
platform/gsutil/third_party/fasteners/tox.ini
platform/gsutil/third_party/funcsigs/.coveragerc
platform/gsutil/third_party/funcsigs/.gitignore
platform/gsutil/third_party/funcsigs/.travis.yml
platform/gsutil/third_party/funcsigs/CHANGELOG
platform/gsutil/third_party/funcsigs/LICENSE
platform/gsutil/third_party/funcsigs/MANIFEST.in
platform/gsutil/third_party/funcsigs/Makefile
platform/gsutil/third_party/funcsigs/README.rst
platform/gsutil/third_party/funcsigs/docs/Makefile
platform/gsutil/third_party/funcsigs/docs/_templates/page.html
platform/gsutil/third_party/funcsigs/docs/conf.py
platform/gsutil/third_party/funcsigs/docs/index.rst
platform/gsutil/third_party/funcsigs/funcsigs/__init__.py
platform/gsutil/third_party/funcsigs/funcsigs/version.py
platform/gsutil/third_party/funcsigs/requirements/development.txt
platform/gsutil/third_party/funcsigs/setup.cfg
platform/gsutil/third_party/funcsigs/setup.py
platform/gsutil/third_party/funcsigs/tests/__init__.py
platform/gsutil/third_party/funcsigs/tests/test_formatannotation.py
platform/gsutil/third_party/funcsigs/tests/test_funcsigs.py
platform/gsutil/third_party/funcsigs/tests/test_inspect.py
platform/gsutil/third_party/funcsigs/tox.ini
platform/gsutil/third_party/gcs-oauth2-boto-plugin/.gitignore
platform/gsutil/third_party/gcs-oauth2-boto-plugin/CONTRIBUTING.md
platform/gsutil/third_party/gcs-oauth2-boto-plugin/LICENSE
platform/gsutil/third_party/gcs-oauth2-boto-plugin/MANIFEST.in
platform/gsutil/third_party/gcs-oauth2-boto-plugin/README.md
platform/gsutil/third_party/gcs-oauth2-boto-plugin/gcs_oauth2_boto_plugin/__init__.py
platform/gsutil/third_party/gcs-oauth2-boto-plugin/gcs_oauth2_boto_plugin/oauth2_client.py
platform/gsutil/third_party/gcs-oauth2-boto-plugin/gcs_oauth2_boto_plugin/oauth2_helper.py
platform/gsutil/third_party/gcs-oauth2-boto-plugin/gcs_oauth2_boto_plugin/oauth2_plugin.py
platform/gsutil/third_party/gcs-oauth2-boto-plugin/gcs_oauth2_boto_plugin/test_oauth2_client.py
platform/gsutil/third_party/gcs-oauth2-boto-plugin/requirements.txt
platform/gsutil/third_party/gcs-oauth2-boto-plugin/setup.cfg
platform/gsutil/third_party/gcs-oauth2-boto-plugin/setup.py
platform/gsutil/third_party/gcs-oauth2-boto-plugin/test-requirements.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/.coveragerc
platform/gsutil/third_party/google-auth-library-python-httplib2/.flake8
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/.OwlBot.lock.yaml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/.OwlBot.yaml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/CODEOWNERS
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/CONTRIBUTING.md
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/ISSUE_TEMPLATE/bug_report.md
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/ISSUE_TEMPLATE/feature_request.md
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/ISSUE_TEMPLATE/support_request.md
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/PULL_REQUEST_TEMPLATE.md
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/auto-approve.yml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/auto-label.yaml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/blunderbuss.yml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/header-checker-lint.yml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/release-please.yml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/release-trigger.yml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/snippet-bot.yml
platform/gsutil/third_party/google-auth-library-python-httplib2/.github/sync-repo-settings.yaml
platform/gsutil/third_party/google-auth-library-python-httplib2/.gitignore
platform/gsutil/third_party/google-auth-library-python-httplib2/.repo-metadata.json
platform/gsutil/third_party/google-auth-library-python-httplib2/CHANGELOG.md
platform/gsutil/third_party/google-auth-library-python-httplib2/CODE_OF_CONDUCT.md
platform/gsutil/third_party/google-auth-library-python-httplib2/LICENSE
platform/gsutil/third_party/google-auth-library-python-httplib2/MANIFEST.in
platform/gsutil/third_party/google-auth-library-python-httplib2/README.rst
platform/gsutil/third_party/google-auth-library-python-httplib2/SECURITY.md
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/CHANGELOG.md
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/README.rst
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/_static/custom.css
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/_templates/layout.html
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/conf.py
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/google_auth_httplib2.rst
platform/gsutil/third_party/google-auth-library-python-httplib2/docs/index.rst
platform/gsutil/third_party/google-auth-library-python-httplib2/google_auth_httplib2.py
platform/gsutil/third_party/google-auth-library-python-httplib2/noxfile.py
platform/gsutil/third_party/google-auth-library-python-httplib2/owlbot.py
platform/gsutil/third_party/google-auth-library-python-httplib2/pylint.config.py
platform/gsutil/third_party/google-auth-library-python-httplib2/pytest.ini
platform/gsutil/third_party/google-auth-library-python-httplib2/renovate.json
platform/gsutil/third_party/google-auth-library-python-httplib2/setup.py
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.10.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.11.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.12.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.13.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.7.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.8.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/testing/constraints-3.9.txt
platform/gsutil/third_party/google-auth-library-python-httplib2/tests/__init__.py
platform/gsutil/third_party/google-auth-library-python-httplib2/tests/compliance.py
platform/gsutil/third_party/google-auth-library-python-httplib2/tests/test_google_auth_httplib2.py
platform/gsutil/third_party/google-auth-library-python-httplib2/tox.ini
platform/gsutil/third_party/google-auth-library-python/.coveragerc
platform/gsutil/third_party/google-auth-library-python/.flake8
platform/gsutil/third_party/google-auth-library-python/.github/.OwlBot.lock.yaml
platform/gsutil/third_party/google-auth-library-python/.github/.OwlBot.yaml
platform/gsutil/third_party/google-auth-library-python/.github/CODEOWNERS
platform/gsutil/third_party/google-auth-library-python/.github/CONTRIBUTING.md
platform/gsutil/third_party/google-auth-library-python/.github/ISSUE_TEMPLATE/bug_report.md
platform/gsutil/third_party/google-auth-library-python/.github/ISSUE_TEMPLATE/feature_request.md
platform/gsutil/third_party/google-auth-library-python/.github/ISSUE_TEMPLATE/support_request.md
platform/gsutil/third_party/google-auth-library-python/.github/release-please.yml
platform/gsutil/third_party/google-auth-library-python/.github/release-trigger.yml
platform/gsutil/third_party/google-auth-library-python/.github/sync-repo-settings.yaml
platform/gsutil/third_party/google-auth-library-python/.gitignore
platform/gsutil/third_party/google-auth-library-python/.readthedocs.yaml
platform/gsutil/third_party/google-auth-library-python/.repo-metadata.json
platform/gsutil/third_party/google-auth-library-python/.trampolinerc
platform/gsutil/third_party/google-auth-library-python/CHANGELOG.md
platform/gsutil/third_party/google-auth-library-python/CODE_OF_CONDUCT.md
platform/gsutil/third_party/google-auth-library-python/CONTRIBUTING.rst
platform/gsutil/third_party/google-auth-library-python/CONTRIBUTORS.md
platform/gsutil/third_party/google-auth-library-python/LICENSE
platform/gsutil/third_party/google-auth-library-python/MANIFEST.in
platform/gsutil/third_party/google-auth-library-python/README.rst
platform/gsutil/third_party/google-auth-library-python/SECURITY.md
platform/gsutil/third_party/google-auth-library-python/docs/_static/custom.css
platform/gsutil/third_party/google-auth-library-python/docs/conf.py
platform/gsutil/third_party/google-auth-library-python/docs/index.rst
platform/gsutil/third_party/google-auth-library-python/docs/oauth2client-deprecation.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth._credentials_async.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth._jwt_async.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.app_engine.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.aws.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.compute_engine.credentials.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.compute_engine.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.credentials.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.crypt.base.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.crypt.es256.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.crypt.rsa.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.crypt.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.downscoped.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.environment_vars.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.exceptions.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.external_account.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.iam.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.identity_pool.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.impersonated_credentials.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.jwt.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.transport._aiohttp_requests.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.transport.grpc.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.transport.mtls.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.transport.requests.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.transport.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.auth.transport.urllib3.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2._credentials_async.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2._service_account_async.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2.credentials.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2.id_token.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2.service_account.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2.sts.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.oauth2.utils.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/google.rst
platform/gsutil/third_party/google-auth-library-python/docs/reference/modules.rst
platform/gsutil/third_party/google-auth-library-python/docs/requirements-docs.txt
platform/gsutil/third_party/google-auth-library-python/docs/user-guide.rst
platform/gsutil/third_party/google-auth-library-python/google/auth/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_cloud_sdk.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_credentials_async.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_credentials_base.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_default.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_default_async.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_exponential_backoff.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_helpers.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_jwt_async.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_oauth2client.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_refresh_worker.py
platform/gsutil/third_party/google-auth-library-python/google/auth/_service_account_info.py
platform/gsutil/third_party/google-auth-library-python/google/auth/aio/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/auth/aio/credentials.py
platform/gsutil/third_party/google-auth-library-python/google/auth/aio/transport/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/auth/aio/transport/aiohttp.py
platform/gsutil/third_party/google-auth-library-python/google/auth/aio/transport/sessions.py
platform/gsutil/third_party/google-auth-library-python/google/auth/api_key.py
platform/gsutil/third_party/google-auth-library-python/google/auth/app_engine.py
platform/gsutil/third_party/google-auth-library-python/google/auth/aws.py
platform/gsutil/third_party/google-auth-library-python/google/auth/compute_engine/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/auth/compute_engine/_metadata.py
platform/gsutil/third_party/google-auth-library-python/google/auth/compute_engine/credentials.py
platform/gsutil/third_party/google-auth-library-python/google/auth/credentials.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/_cryptography_rsa.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/_helpers.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/_python_rsa.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/base.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/es256.py
platform/gsutil/third_party/google-auth-library-python/google/auth/crypt/rsa.py
platform/gsutil/third_party/google-auth-library-python/google/auth/downscoped.py
platform/gsutil/third_party/google-auth-library-python/google/auth/environment_vars.py
platform/gsutil/third_party/google-auth-library-python/google/auth/exceptions.py
platform/gsutil/third_party/google-auth-library-python/google/auth/external_account.py
platform/gsutil/third_party/google-auth-library-python/google/auth/external_account_authorized_user.py
platform/gsutil/third_party/google-auth-library-python/google/auth/iam.py
platform/gsutil/third_party/google-auth-library-python/google/auth/identity_pool.py
platform/gsutil/third_party/google-auth-library-python/google/auth/impersonated_credentials.py
platform/gsutil/third_party/google-auth-library-python/google/auth/jwt.py
platform/gsutil/third_party/google-auth-library-python/google/auth/metrics.py
platform/gsutil/third_party/google-auth-library-python/google/auth/pluggable.py
platform/gsutil/third_party/google-auth-library-python/google/auth/py.typed
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/_aiohttp_requests.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/_custom_tls_signer.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/_http_client.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/_mtls_helper.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/_requests_base.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/grpc.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/mtls.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/requests.py
platform/gsutil/third_party/google-auth-library-python/google/auth/transport/urllib3.py
platform/gsutil/third_party/google-auth-library-python/google/auth/version.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/__init__.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/_client.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/_client_async.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/_credentials_async.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/_id_token_async.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/_reauth_async.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/_service_account_async.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/challenges.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/credentials.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/gdch_credentials.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/id_token.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/py.typed
platform/gsutil/third_party/google-auth-library-python/google/oauth2/reauth.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/service_account.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/sts.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/utils.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/webauthn_handler.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/webauthn_handler_factory.py
platform/gsutil/third_party/google-auth-library-python/google/oauth2/webauthn_types.py
platform/gsutil/third_party/google-auth-library-python/mypy.ini
platform/gsutil/third_party/google-auth-library-python/noxfile.py
platform/gsutil/third_party/google-auth-library-python/owlbot.py
platform/gsutil/third_party/google-auth-library-python/renovate.json
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/authenticate_explicit_with_adc.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/authenticate_implicit_with_adc.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/idtoken_from_impersonated_credentials.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/idtoken_from_metadata_server.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/idtoken_from_service_account.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/noxfile.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/noxfile_config.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/requirements.txt
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/snippets_test.py
platform/gsutil/third_party/google-auth-library-python/samples/cloud-client/snippets/verify_google_idtoken.py
platform/gsutil/third_party/google-auth-library-python/scripts/decrypt-secrets.sh
platform/gsutil/third_party/google-auth-library-python/scripts/encrypt-secrets.sh
platform/gsutil/third_party/google-auth-library-python/scripts/setup_external_accounts.sh
platform/gsutil/third_party/google-auth-library-python/scripts/travis.sh
platform/gsutil/third_party/google-auth-library-python/setup.cfg
platform/gsutil/third_party/google-auth-library-python/setup.py
platform/gsutil/third_party/google-auth-library-python/system_tests/__init__.py
platform/gsutil/third_party/google-auth-library-python/system_tests/noxfile.py
platform/gsutil/third_party/google-auth-library-python/system_tests/secrets.tar.enc
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_async/__init__.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_async/conftest.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_async/test_default.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_async/test_id_token.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_async/test_service_account.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/.gitignore
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/__init__.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/conftest.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/secrets.tar.enc
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_compute_engine.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_default.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_downscoping.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_external_accounts.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_grpc.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_id_token.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_impersonated_credentials.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_mtls_http.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_oauth2_credentials.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_requests.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_service_account.py
platform/gsutil/third_party/google-auth-library-python/system_tests/system_tests_sync/test_urllib3.py
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.10.txt
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.11.txt
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.12.txt
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.13.txt
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.7.txt
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.8.txt
platform/gsutil/third_party/google-auth-library-python/testing/constraints-3.9.txt
platform/gsutil/third_party/google-auth-library-python/tests/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests/compute_engine/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests/compute_engine/data/smbios_product_name
platform/gsutil/third_party/google-auth-library-python/tests/compute_engine/data/smbios_product_name_non_google
platform/gsutil/third_party/google-auth-library-python/tests/compute_engine/test__metadata.py
platform/gsutil/third_party/google-auth-library-python/tests/compute_engine/test_credentials.py
platform/gsutil/third_party/google-auth-library-python/tests/conftest.py
platform/gsutil/third_party/google-auth-library-python/tests/crypt/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests/crypt/test__cryptography_rsa.py
platform/gsutil/third_party/google-auth-library-python/tests/crypt/test__python_rsa.py
platform/gsutil/third_party/google-auth-library-python/tests/crypt/test_crypt.py
platform/gsutil/third_party/google-auth-library-python/tests/crypt/test_es256.py
platform/gsutil/third_party/google-auth-library-python/tests/data/authorized_user.json
platform/gsutil/third_party/google-auth-library-python/tests/data/authorized_user_cloud_sdk.json
platform/gsutil/third_party/google-auth-library-python/tests/data/authorized_user_cloud_sdk_with_quota_project_id.json
platform/gsutil/third_party/google-auth-library-python/tests/data/authorized_user_with_rapt_token.json
platform/gsutil/third_party/google-auth-library-python/tests/data/client_secrets.json
platform/gsutil/third_party/google-auth-library-python/tests/data/context_aware_metadata.json
platform/gsutil/third_party/google-auth-library-python/tests/data/enterprise_cert_invalid.json
platform/gsutil/third_party/google-auth-library-python/tests/data/enterprise_cert_valid.json
platform/gsutil/third_party/google-auth-library-python/tests/data/enterprise_cert_valid_provider.json
platform/gsutil/third_party/google-auth-library-python/tests/data/es256_privatekey.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/es256_public_cert.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/es256_publickey.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/es256_service_account.json
platform/gsutil/third_party/google-auth-library-python/tests/data/external_account_authorized_user.json
platform/gsutil/third_party/google-auth-library-python/tests/data/external_account_authorized_user_non_gdu.json
platform/gsutil/third_party/google-auth-library-python/tests/data/external_subject_token.json
platform/gsutil/third_party/google-auth-library-python/tests/data/external_subject_token.txt
platform/gsutil/third_party/google-auth-library-python/tests/data/gdch_service_account.json
platform/gsutil/third_party/google-auth-library-python/tests/data/impersonated_service_account_authorized_user_source.json
platform/gsutil/third_party/google-auth-library-python/tests/data/impersonated_service_account_external_account_authorized_user_source.json
platform/gsutil/third_party/google-auth-library-python/tests/data/impersonated_service_account_service_account_source.json
platform/gsutil/third_party/google-auth-library-python/tests/data/impersonated_service_account_with_quota_project.json
platform/gsutil/third_party/google-auth-library-python/tests/data/old_oauth_credentials_py3.pickle
platform/gsutil/third_party/google-auth-library-python/tests/data/other_cert.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/pem_from_pkcs12.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/privatekey.p12
platform/gsutil/third_party/google-auth-library-python/tests/data/privatekey.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/privatekey.pub
platform/gsutil/third_party/google-auth-library-python/tests/data/public_cert.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/service_account.json
platform/gsutil/third_party/google-auth-library-python/tests/data/service_account_non_gdu.json
platform/gsutil/third_party/google-auth-library-python/tests/data/trust_chain_with_leaf.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/trust_chain_without_leaf.pem
platform/gsutil/third_party/google-auth-library-python/tests/data/trust_chain_wrong_order.pem
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test__client.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_challenges.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_credentials.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_gdch_credentials.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_id_token.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_reauth.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_service_account.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_sts.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_utils.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_webauthn_handler.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_webauthn_handler_factory.py
platform/gsutil/third_party/google-auth-library-python/tests/oauth2/test_webauthn_types.py
platform/gsutil/third_party/google-auth-library-python/tests/test__cloud_sdk.py
platform/gsutil/third_party/google-auth-library-python/tests/test__default.py
platform/gsutil/third_party/google-auth-library-python/tests/test__exponential_backoff.py
platform/gsutil/third_party/google-auth-library-python/tests/test__helpers.py
platform/gsutil/third_party/google-auth-library-python/tests/test__oauth2client.py
platform/gsutil/third_party/google-auth-library-python/tests/test__refresh_worker.py
platform/gsutil/third_party/google-auth-library-python/tests/test__service_account_info.py
platform/gsutil/third_party/google-auth-library-python/tests/test_api_key.py
platform/gsutil/third_party/google-auth-library-python/tests/test_app_engine.py
platform/gsutil/third_party/google-auth-library-python/tests/test_aws.py
platform/gsutil/third_party/google-auth-library-python/tests/test_credentials.py
platform/gsutil/third_party/google-auth-library-python/tests/test_credentials_async.py
platform/gsutil/third_party/google-auth-library-python/tests/test_downscoped.py
platform/gsutil/third_party/google-auth-library-python/tests/test_exceptions.py
platform/gsutil/third_party/google-auth-library-python/tests/test_external_account.py
platform/gsutil/third_party/google-auth-library-python/tests/test_external_account_authorized_user.py
platform/gsutil/third_party/google-auth-library-python/tests/test_iam.py
platform/gsutil/third_party/google-auth-library-python/tests/test_identity_pool.py
platform/gsutil/third_party/google-auth-library-python/tests/test_impersonated_credentials.py
platform/gsutil/third_party/google-auth-library-python/tests/test_jwt.py
platform/gsutil/third_party/google-auth-library-python/tests/test_metrics.py
platform/gsutil/third_party/google-auth-library-python/tests/test_packaging.py
platform/gsutil/third_party/google-auth-library-python/tests/test_pluggable.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/aio/test_aiohttp.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/aio/test_sessions.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/compliance.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test__custom_tls_signer.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test__http_client.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test__mtls_helper.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test_grpc.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test_mtls.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test_requests.py
platform/gsutil/third_party/google-auth-library-python/tests/transport/test_urllib3.py
platform/gsutil/third_party/google-auth-library-python/tests_async/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests_async/conftest.py
platform/gsutil/third_party/google-auth-library-python/tests_async/oauth2/test__client_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/oauth2/test_credentials_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/oauth2/test_id_token.py
platform/gsutil/third_party/google-auth-library-python/tests_async/oauth2/test_reauth_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/oauth2/test_service_account_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/test__default_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/test_credentials_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/test_jwt_async.py
platform/gsutil/third_party/google-auth-library-python/tests_async/transport/__init__.py
platform/gsutil/third_party/google-auth-library-python/tests_async/transport/async_compliance.py
platform/gsutil/third_party/google-auth-library-python/tests_async/transport/test_aiohttp_requests.py
platform/gsutil/third_party/google-reauth-python/.coveragerc
platform/gsutil/third_party/google-reauth-python/.gitignore
platform/gsutil/third_party/google-reauth-python/.travis.yml
platform/gsutil/third_party/google-reauth-python/CHANGELOG.rst
platform/gsutil/third_party/google-reauth-python/CONTRIBUTING.rst
platform/gsutil/third_party/google-reauth-python/LICENSE
platform/gsutil/third_party/google-reauth-python/MANIFEST.in
platform/gsutil/third_party/google-reauth-python/README.rst
platform/gsutil/third_party/google-reauth-python/google_reauth/__init__.py
platform/gsutil/third_party/google-reauth-python/google_reauth/_helpers.py
platform/gsutil/third_party/google-reauth-python/google_reauth/_reauth_client.py
platform/gsutil/third_party/google-reauth-python/google_reauth/challenges.py
platform/gsutil/third_party/google-reauth-python/google_reauth/errors.py
platform/gsutil/third_party/google-reauth-python/google_reauth/reauth.py
platform/gsutil/third_party/google-reauth-python/google_reauth/reauth_creds.py
platform/gsutil/third_party/google-reauth-python/nox.py
platform/gsutil/third_party/google-reauth-python/noxfile.py
platform/gsutil/third_party/google-reauth-python/setup.cfg
platform/gsutil/third_party/google-reauth-python/setup.py
platform/gsutil/third_party/google-reauth-python/tests/__init__.py
platform/gsutil/third_party/google-reauth-python/tests/test_challenges.py
platform/gsutil/third_party/google-reauth-python/tests/test_reauth.py
platform/gsutil/third_party/google-reauth-python/tests/test_reauth_creds.py
platform/gsutil/third_party/httplib2/.github/workflows/publish.yaml
platform/gsutil/third_party/httplib2/.github/workflows/style.yaml
platform/gsutil/third_party/httplib2/.github/workflows/test.yaml
platform/gsutil/third_party/httplib2/.gitignore
platform/gsutil/third_party/httplib2/CHANGELOG
platform/gsutil/third_party/httplib2/LICENSE
platform/gsutil/third_party/httplib2/MANIFEST.in
platform/gsutil/third_party/httplib2/README.md
platform/gsutil/third_party/httplib2/SECURITY.md
platform/gsutil/third_party/httplib2/codecov.yml
platform/gsutil/third_party/httplib2/libhttplib2.tex
platform/gsutil/third_party/httplib2/pyproject.toml
platform/gsutil/third_party/httplib2/python2/httplib2/__init__.py
platform/gsutil/third_party/httplib2/python2/httplib2/auth.py
platform/gsutil/third_party/httplib2/python2/httplib2/cacerts.txt
platform/gsutil/third_party/httplib2/python2/httplib2/certs.py
platform/gsutil/third_party/httplib2/python2/httplib2/error.py
platform/gsutil/third_party/httplib2/python2/httplib2/iri2uri.py
platform/gsutil/third_party/httplib2/python2/httplib2/socks.py
platform/gsutil/third_party/httplib2/python3/README
platform/gsutil/third_party/httplib2/python3/httplib2/__init__.py
platform/gsutil/third_party/httplib2/python3/httplib2/auth.py
platform/gsutil/third_party/httplib2/python3/httplib2/cacerts.txt
platform/gsutil/third_party/httplib2/python3/httplib2/certs.py
platform/gsutil/third_party/httplib2/python3/httplib2/error.py
platform/gsutil/third_party/httplib2/python3/httplib2/iri2uri.py
platform/gsutil/third_party/httplib2/python3/httplib2/socks.py
platform/gsutil/third_party/httplib2/ref.tex
platform/gsutil/third_party/httplib2/ref/about.html
platform/gsutil/third_party/httplib2/ref/blank.png
platform/gsutil/third_party/httplib2/ref/cache-objects.html
platform/gsutil/third_party/httplib2/ref/contents.html
platform/gsutil/third_party/httplib2/ref/contents.png
platform/gsutil/third_party/httplib2/ref/http-objects.html
platform/gsutil/third_party/httplib2/ref/httplib2-example.html
platform/gsutil/third_party/httplib2/ref/images.idx
platform/gsutil/third_party/httplib2/ref/img1.old
platform/gsutil/third_party/httplib2/ref/img1.png
platform/gsutil/third_party/httplib2/ref/img2.old
platform/gsutil/third_party/httplib2/ref/img2.png
platform/gsutil/third_party/httplib2/ref/index.html
platform/gsutil/third_party/httplib2/ref/index.png
platform/gsutil/third_party/httplib2/ref/modimages.idx
platform/gsutil/third_party/httplib2/ref/module-httplib2.html
platform/gsutil/third_party/httplib2/ref/modules.png
platform/gsutil/third_party/httplib2/ref/next.png
platform/gsutil/third_party/httplib2/ref/node2.html
platform/gsutil/third_party/httplib2/ref/previous.png
platform/gsutil/third_party/httplib2/ref/pyfav.png
platform/gsutil/third_party/httplib2/ref/ref.css
platform/gsutil/third_party/httplib2/ref/ref.html
platform/gsutil/third_party/httplib2/ref/response-objects.html
platform/gsutil/third_party/httplib2/ref/up.png
platform/gsutil/third_party/httplib2/requirements-test.txt
platform/gsutil/third_party/httplib2/requirements.txt
platform/gsutil/third_party/httplib2/script/compile-py3-openssl11.sh
platform/gsutil/third_party/httplib2/script/generate-tls
platform/gsutil/third_party/httplib2/script/release
platform/gsutil/third_party/httplib2/script/test
platform/gsutil/third_party/httplib2/setup.cfg
platform/gsutil/third_party/httplib2/setup.py
platform/gsutil/third_party/idna/.github/workflows/deploy.yml
platform/gsutil/third_party/idna/.github/workflows/python-package.yml
platform/gsutil/third_party/idna/.github/workflows/scorecard.yml
platform/gsutil/third_party/idna/.gitignore
platform/gsutil/third_party/idna/HISTORY.rst
platform/gsutil/third_party/idna/LICENSE.md
platform/gsutil/third_party/idna/README.rst
platform/gsutil/third_party/idna/SECURITY.md
platform/gsutil/third_party/idna/idna/__init__.py
platform/gsutil/third_party/idna/idna/codec.py
platform/gsutil/third_party/idna/idna/compat.py
platform/gsutil/third_party/idna/idna/core.py
platform/gsutil/third_party/idna/idna/idnadata.py
platform/gsutil/third_party/idna/idna/intranges.py
platform/gsutil/third_party/idna/idna/package_data.py
platform/gsutil/third_party/idna/idna/py.typed
platform/gsutil/third_party/idna/idna/uts46data.py
platform/gsutil/third_party/idna/pyproject.toml
platform/gsutil/third_party/idna/tests/IdnaTestV2.txt
platform/gsutil/third_party/idna/tests/__init__.py
platform/gsutil/third_party/idna/tests/test_idna.py
platform/gsutil/third_party/idna/tests/test_idna_codec.py
platform/gsutil/third_party/idna/tests/test_idna_compat.py
platform/gsutil/third_party/idna/tests/test_idna_other.py
platform/gsutil/third_party/idna/tests/test_idna_uts46.py
platform/gsutil/third_party/idna/tests/test_intranges.py
platform/gsutil/third_party/idna/tools/idna-data
platform/gsutil/third_party/mock/.gitignore
platform/gsutil/third_party/mock/.testr.conf
platform/gsutil/third_party/mock/.travis.yml
platform/gsutil/third_party/mock/ChangeLog
platform/gsutil/third_party/mock/LICENSE.txt
platform/gsutil/third_party/mock/NEWS
platform/gsutil/third_party/mock/README.rst
platform/gsutil/third_party/mock/docs/changelog.txt
platform/gsutil/third_party/mock/docs/conf.py
platform/gsutil/third_party/mock/docs/index.txt
platform/gsutil/third_party/mock/extendmock.py
platform/gsutil/third_party/mock/mock.wpr
platform/gsutil/third_party/mock/mock/__init__.py
platform/gsutil/third_party/mock/mock/mock.py
platform/gsutil/third_party/mock/mock/tests/__init__.py
platform/gsutil/third_party/mock/mock/tests/__main__.py
platform/gsutil/third_party/mock/mock/tests/support.py
platform/gsutil/third_party/mock/mock/tests/testcallable.py
platform/gsutil/third_party/mock/mock/tests/testhelpers.py
platform/gsutil/third_party/mock/mock/tests/testmagicmethods.py
platform/gsutil/third_party/mock/mock/tests/testmock.py
platform/gsutil/third_party/mock/mock/tests/testpatch.py
platform/gsutil/third_party/mock/mock/tests/testsentinel.py
platform/gsutil/third_party/mock/mock/tests/testwith.py
platform/gsutil/third_party/mock/requirements.txt
platform/gsutil/third_party/mock/setup.cfg
platform/gsutil/third_party/mock/setup.py
platform/gsutil/third_party/mock/tools/applypatch-transform
platform/gsutil/third_party/mock/tools/pre-applypatch
platform/gsutil/third_party/mock/tox.ini
platform/gsutil/third_party/mock/unittest.cfg
platform/gsutil/third_party/monotonic/.gitignore
platform/gsutil/third_party/monotonic/LICENSE
platform/gsutil/third_party/monotonic/MANIFEST.in
platform/gsutil/third_party/monotonic/README.md
platform/gsutil/third_party/monotonic/monotonic.py
platform/gsutil/third_party/monotonic/setup.cfg
platform/gsutil/third_party/monotonic/setup.py
platform/gsutil/third_party/pyasn1-modules/CHANGES.txt
platform/gsutil/third_party/pyasn1-modules/LICENSE.txt
platform/gsutil/third_party/pyasn1-modules/MANIFEST.in
platform/gsutil/third_party/pyasn1-modules/PKG-INFO
platform/gsutil/third_party/pyasn1-modules/README.md
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/__init__.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/pem.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc1155.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc1157.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc1901.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc1902.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc1905.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2251.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2314.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2315.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2437.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2459.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2511.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc2560.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3279.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3280.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3281.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3412.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3414.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3447.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc3852.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc4210.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc4211.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc5208.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc5280.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc5652.py
platform/gsutil/third_party/pyasn1-modules/pyasn1_modules/rfc6402.py
platform/gsutil/third_party/pyasn1-modules/requirements.txt
platform/gsutil/third_party/pyasn1-modules/setup.cfg
platform/gsutil/third_party/pyasn1-modules/setup.py
platform/gsutil/third_party/pyasn1-modules/tests/__init__.py
platform/gsutil/third_party/pyasn1-modules/tests/__main__.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc2314.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc2315.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc2437.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc2459.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc2511.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc2560.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc4210.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc5208.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc5280.py
platform/gsutil/third_party/pyasn1-modules/tests/test_rfc5652.py
platform/gsutil/third_party/pyasn1-modules/tools/cmcdump.py
platform/gsutil/third_party/pyasn1-modules/tools/cmpdump.py
platform/gsutil/third_party/pyasn1-modules/tools/crldump.py
platform/gsutil/third_party/pyasn1-modules/tools/crmfdump.py
platform/gsutil/third_party/pyasn1-modules/tools/ocspclient.py
platform/gsutil/third_party/pyasn1-modules/tools/ocspreqdump.py
platform/gsutil/third_party/pyasn1-modules/tools/ocsprspdump.py
platform/gsutil/third_party/pyasn1-modules/tools/pkcs10dump.py
platform/gsutil/third_party/pyasn1-modules/tools/pkcs1dump.py
platform/gsutil/third_party/pyasn1-modules/tools/pkcs7dump.py
platform/gsutil/third_party/pyasn1-modules/tools/pkcs8dump.py
platform/gsutil/third_party/pyasn1-modules/tools/snmpget.py
platform/gsutil/third_party/pyasn1-modules/tools/x509dump-rfc5280.py
platform/gsutil/third_party/pyasn1-modules/tools/x509dump.py
platform/gsutil/third_party/pyasn1/.bandit.yml
platform/gsutil/third_party/pyasn1/.github/FUNDING.yml
platform/gsutil/third_party/pyasn1/.github/workflows/main.yml
platform/gsutil/third_party/pyasn1/.github/workflows/pypi.yml
platform/gsutil/third_party/pyasn1/.gitignore
platform/gsutil/third_party/pyasn1/.readthedocs.yaml
platform/gsutil/third_party/pyasn1/CHANGES.rst
platform/gsutil/third_party/pyasn1/LICENSE.rst
platform/gsutil/third_party/pyasn1/MANIFEST.in
platform/gsutil/third_party/pyasn1/README.md
platform/gsutil/third_party/pyasn1/THANKS.txt
platform/gsutil/third_party/pyasn1/TODO.rst
platform/gsutil/third_party/pyasn1/devel-requirements.txt
platform/gsutil/third_party/pyasn1/docs/Makefile
platform/gsutil/third_party/pyasn1/docs/source/.static/css/rtdimproved.css
platform/gsutil/third_party/pyasn1/docs/source/.static/favicon.ico
platform/gsutil/third_party/pyasn1/docs/source/.static/logo.svg
platform/gsutil/third_party/pyasn1/docs/source/changelog.rst
platform/gsutil/third_party/pyasn1/docs/source/conf.py
platform/gsutil/third_party/pyasn1/docs/source/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/download.rst
platform/gsutil/third_party/pyasn1/docs/source/example-use-case.rst
platform/gsutil/third_party/pyasn1/docs/source/index.rst
platform/gsutil/third_party/pyasn1/docs/source/license.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/codec/ber/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/codec/cer/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/codec/der/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/codec/native/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/error/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/base/asn1type.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/base/constructedasn1type.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/base/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/base/novalue.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/base/simpleasn1type.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/bmpstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/generalstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/graphicstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/ia5string.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/iso646string.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/numericstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/printablestring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/t61string.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/teletexstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/universalstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/utf8string.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/videotexstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/char/visiblestring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/constraintsexclusion.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/constraintsintersection.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/constraintsunion.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/containedsubtype.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/permittedalphabet.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/singlevalue.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/valuerange.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/valuesize.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/constraint/withcomponents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedtype/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedtype/defaultednamedtype.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedtype/namedtype.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedtype/namedtypes.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedtype/optionalnamedtype.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedval/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/namedval/namedval.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/opentype/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/opentype/opentype.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/tag/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/tag/tag.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/tag/tagmap.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/tag/tagset.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/any.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/bitstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/boolean.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/choice.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/enumerated.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/integer.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/null.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/objectidentifier.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/octetstring.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/real.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/sequence.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/sequenceof.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/set.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/univ/setof.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/useful/contents.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/useful/generalizedtime.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/useful/objectdescriptor.rst
platform/gsutil/third_party/pyasn1/docs/source/pyasn1/type/useful/utctime.rst
platform/gsutil/third_party/pyasn1/pyasn1/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/ber/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/ber/decoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/ber/encoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/ber/eoo.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/cer/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/cer/decoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/cer/encoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/der/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/der/decoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/der/encoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/native/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/native/decoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/native/encoder.py
platform/gsutil/third_party/pyasn1/pyasn1/codec/streaming.py
platform/gsutil/third_party/pyasn1/pyasn1/compat/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/compat/integer.py
platform/gsutil/third_party/pyasn1/pyasn1/debug.py
platform/gsutil/third_party/pyasn1/pyasn1/error.py
platform/gsutil/third_party/pyasn1/pyasn1/type/__init__.py
platform/gsutil/third_party/pyasn1/pyasn1/type/base.py
platform/gsutil/third_party/pyasn1/pyasn1/type/char.py
platform/gsutil/third_party/pyasn1/pyasn1/type/constraint.py
platform/gsutil/third_party/pyasn1/pyasn1/type/error.py
platform/gsutil/third_party/pyasn1/pyasn1/type/namedtype.py
platform/gsutil/third_party/pyasn1/pyasn1/type/namedval.py
platform/gsutil/third_party/pyasn1/pyasn1/type/opentype.py
platform/gsutil/third_party/pyasn1/pyasn1/type/tag.py
platform/gsutil/third_party/pyasn1/pyasn1/type/tagmap.py
platform/gsutil/third_party/pyasn1/pyasn1/type/univ.py
platform/gsutil/third_party/pyasn1/pyasn1/type/useful.py
platform/gsutil/third_party/pyasn1/pyproject.toml
platform/gsutil/third_party/pyasn1/requirements.txt
platform/gsutil/third_party/pyasn1/setup.cfg
platform/gsutil/third_party/pyasn1/setup.py
platform/gsutil/third_party/pyasn1/tests/__init__.py
platform/gsutil/third_party/pyasn1/tests/__main__.py
platform/gsutil/third_party/pyasn1/tests/base.py
platform/gsutil/third_party/pyasn1/tests/codec/__init__.py
platform/gsutil/third_party/pyasn1/tests/codec/__main__.py
platform/gsutil/third_party/pyasn1/tests/codec/ber/__init__.py
platform/gsutil/third_party/pyasn1/tests/codec/ber/__main__.py
platform/gsutil/third_party/pyasn1/tests/codec/ber/test_decoder.py
platform/gsutil/third_party/pyasn1/tests/codec/ber/test_encoder.py
platform/gsutil/third_party/pyasn1/tests/codec/cer/__init__.py
platform/gsutil/third_party/pyasn1/tests/codec/cer/__main__.py
platform/gsutil/third_party/pyasn1/tests/codec/cer/test_decoder.py
platform/gsutil/third_party/pyasn1/tests/codec/cer/test_encoder.py
platform/gsutil/third_party/pyasn1/tests/codec/der/__init__.py
platform/gsutil/third_party/pyasn1/tests/codec/der/__main__.py
platform/gsutil/third_party/pyasn1/tests/codec/der/test_decoder.py
platform/gsutil/third_party/pyasn1/tests/codec/der/test_encoder.py
platform/gsutil/third_party/pyasn1/tests/codec/native/__init__.py
platform/gsutil/third_party/pyasn1/tests/codec/native/__main__.py
platform/gsutil/third_party/pyasn1/tests/codec/native/test_decoder.py
platform/gsutil/third_party/pyasn1/tests/codec/native/test_encoder.py
platform/gsutil/third_party/pyasn1/tests/codec/test_streaming.py
platform/gsutil/third_party/pyasn1/tests/compat/__init__.py
platform/gsutil/third_party/pyasn1/tests/compat/__main__.py
platform/gsutil/third_party/pyasn1/tests/test_debug.py
platform/gsutil/third_party/pyasn1/tests/type/__init__.py
platform/gsutil/third_party/pyasn1/tests/type/__main__.py
platform/gsutil/third_party/pyasn1/tests/type/test_char.py
platform/gsutil/third_party/pyasn1/tests/type/test_constraint.py
platform/gsutil/third_party/pyasn1/tests/type/test_namedtype.py
platform/gsutil/third_party/pyasn1/tests/type/test_namedval.py
platform/gsutil/third_party/pyasn1/tests/type/test_opentype.py
platform/gsutil/third_party/pyasn1/tests/type/test_tag.py
platform/gsutil/third_party/pyasn1/tests/type/test_univ.py
platform/gsutil/third_party/pyasn1/tests/type/test_useful.py
platform/gsutil/third_party/pyasn1/tox.ini
platform/gsutil/third_party/pyparsing/.coveragerc
platform/gsutil/third_party/pyparsing/.github/FUNDING.yml
platform/gsutil/third_party/pyparsing/.github/SECURITY.md
platform/gsutil/third_party/pyparsing/.github/workflows/ci.yml
platform/gsutil/third_party/pyparsing/.gitignore
platform/gsutil/third_party/pyparsing/.pre-commit-config.yaml
platform/gsutil/third_party/pyparsing/BUILDING.md
platform/gsutil/third_party/pyparsing/CHANGES
platform/gsutil/third_party/pyparsing/CODE_OF_CONDUCT.rst
platform/gsutil/third_party/pyparsing/CONTRIBUTING.md
platform/gsutil/third_party/pyparsing/LICENSE
platform/gsutil/third_party/pyparsing/README.rst
platform/gsutil/third_party/pyparsing/docs/CODE_OF_CONDUCT.rst
platform/gsutil/third_party/pyparsing/docs/HowToUsePyparsing.rst
platform/gsutil/third_party/pyparsing/docs/Makefile
platform/gsutil/third_party/pyparsing/docs/_static/pyparsingClassDiagram_1.5.2.jpg
platform/gsutil/third_party/pyparsing/docs/_static/pyparsingClassDiagram_3.0.9.jpg
platform/gsutil/third_party/pyparsing/docs/_static/sql_railroad.html
platform/gsutil/third_party/pyparsing/docs/conf.py
platform/gsutil/third_party/pyparsing/docs/index.rst
platform/gsutil/third_party/pyparsing/docs/make_sphinx_docs.bat
platform/gsutil/third_party/pyparsing/docs/modules.rst
platform/gsutil/third_party/pyparsing/docs/pyparsing.rst
platform/gsutil/third_party/pyparsing/docs/pyparsing_class_diagram.puml
platform/gsutil/third_party/pyparsing/docs/whats_new_in_3_0_0.rst
platform/gsutil/third_party/pyparsing/docs/whats_new_in_3_1.rst
platform/gsutil/third_party/pyparsing/docs/whats_new_in_3_2.rst
platform/gsutil/third_party/pyparsing/docs/whats_new_in_x_x_template.rst.txt
platform/gsutil/third_party/pyparsing/examples/AcManForm.dfm
platform/gsutil/third_party/pyparsing/examples/LAparser.py
platform/gsutil/third_party/pyparsing/examples/README.md
platform/gsutil/third_party/pyparsing/examples/Setup.ini
platform/gsutil/third_party/pyparsing/examples/SimpleCalc.py
platform/gsutil/third_party/pyparsing/examples/SingleForm.dfm
platform/gsutil/third_party/pyparsing/examples/TAP.py
platform/gsutil/third_party/pyparsing/examples/TAP_diagram.html
platform/gsutil/third_party/pyparsing/examples/__init__.py
platform/gsutil/third_party/pyparsing/examples/adventureEngine.py
platform/gsutil/third_party/pyparsing/examples/adventure_game_parser_diagram.html
platform/gsutil/third_party/pyparsing/examples/antlr_grammar.py
platform/gsutil/third_party/pyparsing/examples/antlr_grammar_diagram.html
platform/gsutil/third_party/pyparsing/examples/antlr_grammar_tests.py
platform/gsutil/third_party/pyparsing/examples/apicheck.py
platform/gsutil/third_party/pyparsing/examples/apicheck_diagram.html
platform/gsutil/third_party/pyparsing/examples/bf.py
platform/gsutil/third_party/pyparsing/examples/bf_diagram.html
platform/gsutil/third_party/pyparsing/examples/bigquery_view_parser.py
platform/gsutil/third_party/pyparsing/examples/booleansearchparser.py
platform/gsutil/third_party/pyparsing/examples/btpyparse.py
platform/gsutil/third_party/pyparsing/examples/builtin_parse_action_demo.py
platform/gsutil/third_party/pyparsing/examples/cLibHeader.py
platform/gsutil/third_party/pyparsing/examples/chemical_formulas.html
platform/gsutil/third_party/pyparsing/examples/chemical_formulas.py
platform/gsutil/third_party/pyparsing/examples/commasep.py
platform/gsutil/third_party/pyparsing/examples/complex_chemical_formulas.py
platform/gsutil/third_party/pyparsing/examples/complex_chemical_formulas_diagram.html
platform/gsutil/third_party/pyparsing/examples/configParse.py
platform/gsutil/third_party/pyparsing/examples/cpp_enum_parser.py
platform/gsutil/third_party/pyparsing/examples/cuneiform_python.py
platform/gsutil/third_party/pyparsing/examples/datetime_parse_actions.py
platform/gsutil/third_party/pyparsing/examples/decaf_parser.py
platform/gsutil/third_party/pyparsing/examples/decaf_parser_diagram.html
platform/gsutil/third_party/pyparsing/examples/delta_time.py
platform/gsutil/third_party/pyparsing/examples/delta_time_diagram.html
platform/gsutil/third_party/pyparsing/examples/dfmparse.py
platform/gsutil/third_party/pyparsing/examples/dhcpd_leases_parser.py
platform/gsutil/third_party/pyparsing/examples/dictExample.py
platform/gsutil/third_party/pyparsing/examples/dictExample2.py
platform/gsutil/third_party/pyparsing/examples/directx_x_file_parser.html
platform/gsutil/third_party/pyparsing/examples/directx_x_file_parser.py
platform/gsutil/third_party/pyparsing/examples/ebnf.py
platform/gsutil/third_party/pyparsing/examples/ebnf_diagram.html
platform/gsutil/third_party/pyparsing/examples/ebnf_number_parser_diagram.html
platform/gsutil/third_party/pyparsing/examples/ebnf_number_words.py
platform/gsutil/third_party/pyparsing/examples/ebnftest.py
platform/gsutil/third_party/pyparsing/examples/email_address_parser.py
platform/gsutil/third_party/pyparsing/examples/eval_arith.py
platform/gsutil/third_party/pyparsing/examples/excel_expr.py
platform/gsutil/third_party/pyparsing/examples/fourFn.py
platform/gsutil/third_party/pyparsing/examples/gen_ctypes.py
platform/gsutil/third_party/pyparsing/examples/getNTPserversNew.py
platform/gsutil/third_party/pyparsing/examples/greeting.py
platform/gsutil/third_party/pyparsing/examples/greetingInGreek.py
platform/gsutil/third_party/pyparsing/examples/greetingInKorean.py
platform/gsutil/third_party/pyparsing/examples/groupUsingListAllMatches.py
platform/gsutil/third_party/pyparsing/examples/hola_mundo.py
platform/gsutil/third_party/pyparsing/examples/html_stripper.py
platform/gsutil/third_party/pyparsing/examples/html_table_parser.py
platform/gsutil/third_party/pyparsing/examples/httpServerLogParser.py
platform/gsutil/third_party/pyparsing/examples/idlParse.py
platform/gsutil/third_party/pyparsing/examples/include_preprocessor.py
platform/gsutil/third_party/pyparsing/examples/indentedGrammarExample.py
platform/gsutil/third_party/pyparsing/examples/indented_block_example.py
platform/gsutil/third_party/pyparsing/examples/infix_math_parser.py
platform/gsutil/third_party/pyparsing/examples/inv_regex.py
platform/gsutil/third_party/pyparsing/examples/javascript_grammar.g
platform/gsutil/third_party/pyparsing/examples/jsonParser.py
platform/gsutil/third_party/pyparsing/examples/left_recursion.py
platform/gsutil/third_party/pyparsing/examples/lineno_example.py
platform/gsutil/third_party/pyparsing/examples/listAllMatches.py
platform/gsutil/third_party/pyparsing/examples/lox_parser.py
platform/gsutil/third_party/pyparsing/examples/lox_parser_diagram.html
platform/gsutil/third_party/pyparsing/examples/lua_parser.py
platform/gsutil/third_party/pyparsing/examples/lua_parser_diagram.html
platform/gsutil/third_party/pyparsing/examples/lucene_grammar.py
platform/gsutil/third_party/pyparsing/examples/lucene_grammar_diagram.html
platform/gsutil/third_party/pyparsing/examples/macro_expander.py
platform/gsutil/third_party/pyparsing/examples/make_diagram.py
platform/gsutil/third_party/pyparsing/examples/matchPreviousDemo.py
platform/gsutil/third_party/pyparsing/examples/mongodb_query_expression.html
platform/gsutil/third_party/pyparsing/examples/mongodb_query_expression.py
platform/gsutil/third_party/pyparsing/examples/mongodb_query_expression_step_0.py
platform/gsutil/third_party/pyparsing/examples/mozilla.ics
platform/gsutil/third_party/pyparsing/examples/mozillaCalendarParser.py
platform/gsutil/third_party/pyparsing/examples/nested.py
platform/gsutil/third_party/pyparsing/examples/nested_markup.py
platform/gsutil/third_party/pyparsing/examples/number_words.py
platform/gsutil/third_party/pyparsing/examples/number_words_diagram.html
platform/gsutil/third_party/pyparsing/examples/numerics.py
platform/gsutil/third_party/pyparsing/examples/oc.py
platform/gsutil/third_party/pyparsing/examples/one_to_ninety_nine.py
platform/gsutil/third_party/pyparsing/examples/parseTabularData.py
platform/gsutil/third_party/pyparsing/examples/parse_python_value.html
platform/gsutil/third_party/pyparsing/examples/parse_python_value.py
platform/gsutil/third_party/pyparsing/examples/parse_results_sum_example.py
platform/gsutil/third_party/pyparsing/examples/partial_gene_match.py
platform/gsutil/third_party/pyparsing/examples/pgn.py
platform/gsutil/third_party/pyparsing/examples/position.py
platform/gsutil/third_party/pyparsing/examples/protobuf_parser.py
platform/gsutil/third_party/pyparsing/examples/pythonGrammarParser.py
platform/gsutil/third_party/pyparsing/examples/railroad_diagram_demo.py
platform/gsutil/third_party/pyparsing/examples/range_check.py
platform/gsutil/third_party/pyparsing/examples/readJson.py
platform/gsutil/third_party/pyparsing/examples/removeLineBreaks.py
platform/gsutil/third_party/pyparsing/examples/roman_numerals.py
platform/gsutil/third_party/pyparsing/examples/roman_numerals_diagram.html
platform/gsutil/third_party/pyparsing/examples/rosettacode.py
platform/gsutil/third_party/pyparsing/examples/rosettacode_diagram.html
platform/gsutil/third_party/pyparsing/examples/scanExamples.py
platform/gsutil/third_party/pyparsing/examples/searchParserAppDemo.py
platform/gsutil/third_party/pyparsing/examples/searchparser.py
platform/gsutil/third_party/pyparsing/examples/select_parser.py
platform/gsutil/third_party/pyparsing/examples/sexpParser.py
platform/gsutil/third_party/pyparsing/examples/shapes.py
platform/gsutil/third_party/pyparsing/examples/simpleArith.py
platform/gsutil/third_party/pyparsing/examples/simpleBool.py
platform/gsutil/third_party/pyparsing/examples/simpleSQL.py
platform/gsutil/third_party/pyparsing/examples/simpleWiki.py
platform/gsutil/third_party/pyparsing/examples/snmp_api.h
platform/gsutil/third_party/pyparsing/examples/sql2dot.py
platform/gsutil/third_party/pyparsing/examples/stackish.py
platform/gsutil/third_party/pyparsing/examples/statemachine/documentSignoffDemo.py
platform/gsutil/third_party/pyparsing/examples/statemachine/documentsignoffstate.pystate
platform/gsutil/third_party/pyparsing/examples/statemachine/libraryBookDemo.py
platform/gsutil/third_party/pyparsing/examples/statemachine/librarybookstate.pystate
platform/gsutil/third_party/pyparsing/examples/statemachine/statemachine.py
platform/gsutil/third_party/pyparsing/examples/statemachine/trafficLightDemo.py
platform/gsutil/third_party/pyparsing/examples/statemachine/trafficlightstate.pystate
platform/gsutil/third_party/pyparsing/examples/statemachine/vending_machine.py
platform/gsutil/third_party/pyparsing/examples/statemachine/video_demo.py
platform/gsutil/third_party/pyparsing/examples/statemachine/videostate.pystate
platform/gsutil/third_party/pyparsing/examples/tag_emitter.py
platform/gsutil/third_party/pyparsing/examples/tag_metadata.py
platform/gsutil/third_party/pyparsing/examples/tag_metadata_diagram.html
platform/gsutil/third_party/pyparsing/examples/test_bibparse.py
platform/gsutil/third_party/pyparsing/examples/unicode_denormalizer.py
platform/gsutil/third_party/pyparsing/examples/urlExtractor.py
platform/gsutil/third_party/pyparsing/examples/urlExtractorNew.py
platform/gsutil/third_party/pyparsing/examples/verilog_parse.py
platform/gsutil/third_party/pyparsing/examples/withAttribute.py
platform/gsutil/third_party/pyparsing/examples/wordsToNum.py
platform/gsutil/third_party/pyparsing/pyparsing/__init__.py
platform/gsutil/third_party/pyparsing/pyparsing/actions.py
platform/gsutil/third_party/pyparsing/pyparsing/common.py
platform/gsutil/third_party/pyparsing/pyparsing/core.py
platform/gsutil/third_party/pyparsing/pyparsing/diagram/__init__.py
platform/gsutil/third_party/pyparsing/pyparsing/exceptions.py
platform/gsutil/third_party/pyparsing/pyparsing/helpers.py
platform/gsutil/third_party/pyparsing/pyparsing/py.typed
platform/gsutil/third_party/pyparsing/pyparsing/results.py
platform/gsutil/third_party/pyparsing/pyparsing/testing.py
platform/gsutil/third_party/pyparsing/pyparsing/tools/__init__.py
platform/gsutil/third_party/pyparsing/pyparsing/tools/cvt_pyparsing_pep8_names.py
platform/gsutil/third_party/pyparsing/pyparsing/unicode.py
platform/gsutil/third_party/pyparsing/pyparsing/util.py
platform/gsutil/third_party/pyparsing/pyproject.toml
platform/gsutil/third_party/pyparsing/readthedocs.yaml
platform/gsutil/third_party/pyparsing/tests/README.md
platform/gsutil/third_party/pyparsing/tests/__init__.py
platform/gsutil/third_party/pyparsing/tests/diag_embed.html
platform/gsutil/third_party/pyparsing/tests/diag_no_embed.html
platform/gsutil/third_party/pyparsing/tests/json_parser_tests.py
platform/gsutil/third_party/pyparsing/tests/karthik.ini
platform/gsutil/third_party/pyparsing/tests/mypy-ignore-cases/forward_methods.py
platform/gsutil/third_party/pyparsing/tests/parsefiletest_input_file.txt
platform/gsutil/third_party/pyparsing/tests/requirements.txt
platform/gsutil/third_party/pyparsing/tests/test_diagram.py
platform/gsutil/third_party/pyparsing/tests/test_examples.py
platform/gsutil/third_party/pyparsing/tests/test_matplotlib_cases.py
platform/gsutil/third_party/pyparsing/tests/test_pep8_converter.py
platform/gsutil/third_party/pyparsing/tests/test_simple_unit.py
platform/gsutil/third_party/pyparsing/tests/test_unit.py
platform/gsutil/third_party/pyparsing/tests/test_util.py
platform/gsutil/third_party/pyparsing/tox.ini
platform/gsutil/third_party/pyparsing/update_pyparsing_timestamp.py
platform/gsutil/third_party/pyu2f/.gitignore
platform/gsutil/third_party/pyu2f/.travis.yml
platform/gsutil/third_party/pyu2f/CONTRIBUTING.md
platform/gsutil/third_party/pyu2f/LICENSE
platform/gsutil/third_party/pyu2f/MANIFEST.in
platform/gsutil/third_party/pyu2f/README.md
platform/gsutil/third_party/pyu2f/pyu2f/__init__.py
platform/gsutil/third_party/pyu2f/pyu2f/apdu.py
platform/gsutil/third_party/pyu2f/pyu2f/convenience/__init__.py
platform/gsutil/third_party/pyu2f/pyu2f/convenience/authenticator.py
platform/gsutil/third_party/pyu2f/pyu2f/convenience/baseauthenticator.py
platform/gsutil/third_party/pyu2f/pyu2f/convenience/customauthenticator.py
platform/gsutil/third_party/pyu2f/pyu2f/convenience/localauthenticator.py
platform/gsutil/third_party/pyu2f/pyu2f/errors.py
platform/gsutil/third_party/pyu2f/pyu2f/hardware.py
platform/gsutil/third_party/pyu2f/pyu2f/hid/__init__.py
platform/gsutil/third_party/pyu2f/pyu2f/hid/base.py
platform/gsutil/third_party/pyu2f/pyu2f/hid/linux.py
platform/gsutil/third_party/pyu2f/pyu2f/hid/macos.py
platform/gsutil/third_party/pyu2f/pyu2f/hid/try.py
platform/gsutil/third_party/pyu2f/pyu2f/hid/windows.py
platform/gsutil/third_party/pyu2f/pyu2f/hidtransport.py
platform/gsutil/third_party/pyu2f/pyu2f/model.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/__init__.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/apdu_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/customauthenticator_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/hardware_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/hid/linux_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/hid/macos_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/hidtransport_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/lib/__init__.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/lib/util.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/localauthenticator_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/model_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/u2f_test.py
platform/gsutil/third_party/pyu2f/pyu2f/tests/util_test.py
platform/gsutil/third_party/pyu2f/pyu2f/u2f.py
platform/gsutil/third_party/pyu2f/setup.cfg
platform/gsutil/third_party/pyu2f/setup.py
platform/gsutil/third_party/pyu2f/tox.ini
platform/gsutil/third_party/requests/.coveragerc
platform/gsutil/third_party/requests/.git-blame-ignore-revs
platform/gsutil/third_party/requests/.github/CODE_OF_CONDUCT.md
platform/gsutil/third_party/requests/.github/CONTRIBUTING.md
platform/gsutil/third_party/requests/.github/FUNDING.yml
platform/gsutil/third_party/requests/.github/ISSUE_TEMPLATE.md
platform/gsutil/third_party/requests/.github/ISSUE_TEMPLATE/Bug_report.md
platform/gsutil/third_party/requests/.github/ISSUE_TEMPLATE/Custom.md
platform/gsutil/third_party/requests/.github/ISSUE_TEMPLATE/Feature_request.md
platform/gsutil/third_party/requests/.github/SECURITY.md
platform/gsutil/third_party/requests/.github/dependabot.yml
platform/gsutil/third_party/requests/.github/workflows/close-issues.yml
platform/gsutil/third_party/requests/.github/workflows/codeql-analysis.yml
platform/gsutil/third_party/requests/.github/workflows/lint.yml
platform/gsutil/third_party/requests/.github/workflows/lock-issues.yml
platform/gsutil/third_party/requests/.github/workflows/run-tests.yml
platform/gsutil/third_party/requests/.gitignore
platform/gsutil/third_party/requests/.pre-commit-config.yaml
platform/gsutil/third_party/requests/.readthedocs.yaml
platform/gsutil/third_party/requests/AUTHORS.rst
platform/gsutil/third_party/requests/HISTORY.md
platform/gsutil/third_party/requests/LICENSE
platform/gsutil/third_party/requests/MANIFEST.in
platform/gsutil/third_party/requests/Makefile
platform/gsutil/third_party/requests/NOTICE
platform/gsutil/third_party/requests/README.md
platform/gsutil/third_party/requests/docs/.nojekyll
platform/gsutil/third_party/requests/docs/Makefile
platform/gsutil/third_party/requests/docs/_static/custom.css
platform/gsutil/third_party/requests/docs/_static/requests-sidebar.png
platform/gsutil/third_party/requests/docs/_templates/hacks.html
platform/gsutil/third_party/requests/docs/_templates/sidebarintro.html
platform/gsutil/third_party/requests/docs/_templates/sidebarlogo.html
platform/gsutil/third_party/requests/docs/_themes/.gitignore
platform/gsutil/third_party/requests/docs/_themes/LICENSE
platform/gsutil/third_party/requests/docs/_themes/flask_theme_support.py
platform/gsutil/third_party/requests/docs/api.rst
platform/gsutil/third_party/requests/docs/community/faq.rst
platform/gsutil/third_party/requests/docs/community/out-there.rst
platform/gsutil/third_party/requests/docs/community/recommended.rst
platform/gsutil/third_party/requests/docs/community/release-process.rst
platform/gsutil/third_party/requests/docs/community/support.rst
platform/gsutil/third_party/requests/docs/community/updates.rst
platform/gsutil/third_party/requests/docs/community/vulnerabilities.rst
platform/gsutil/third_party/requests/docs/conf.py
platform/gsutil/third_party/requests/docs/dev/authors.rst
platform/gsutil/third_party/requests/docs/dev/contributing.rst
platform/gsutil/third_party/requests/docs/index.rst
platform/gsutil/third_party/requests/docs/make.bat
platform/gsutil/third_party/requests/docs/requirements.txt
platform/gsutil/third_party/requests/docs/user/advanced.rst
platform/gsutil/third_party/requests/docs/user/authentication.rst
platform/gsutil/third_party/requests/docs/user/install.rst
platform/gsutil/third_party/requests/docs/user/quickstart.rst
platform/gsutil/third_party/requests/ext/LICENSE
platform/gsutil/third_party/requests/ext/flower-of-life.jpg
platform/gsutil/third_party/requests/ext/kr-compressed.png
platform/gsutil/third_party/requests/ext/kr.png
platform/gsutil/third_party/requests/ext/psf-compressed.png
platform/gsutil/third_party/requests/ext/psf.png
platform/gsutil/third_party/requests/ext/requests-logo-compressed.png
platform/gsutil/third_party/requests/ext/requests-logo.ai
platform/gsutil/third_party/requests/ext/requests-logo.png
platform/gsutil/third_party/requests/ext/requests-logo.svg
platform/gsutil/third_party/requests/ext/ss-compressed.png
platform/gsutil/third_party/requests/ext/ss.png
platform/gsutil/third_party/requests/pyproject.toml
platform/gsutil/third_party/requests/requirements-dev.txt
platform/gsutil/third_party/requests/setup.cfg
platform/gsutil/third_party/requests/setup.py
platform/gsutil/third_party/requests/src/requests/__init__.py
platform/gsutil/third_party/requests/src/requests/__version__.py
platform/gsutil/third_party/requests/src/requests/_internal_utils.py
platform/gsutil/third_party/requests/src/requests/adapters.py
platform/gsutil/third_party/requests/src/requests/api.py
platform/gsutil/third_party/requests/src/requests/auth.py
platform/gsutil/third_party/requests/src/requests/certs.py
platform/gsutil/third_party/requests/src/requests/compat.py
platform/gsutil/third_party/requests/src/requests/cookies.py
platform/gsutil/third_party/requests/src/requests/exceptions.py
platform/gsutil/third_party/requests/src/requests/help.py
platform/gsutil/third_party/requests/src/requests/hooks.py
platform/gsutil/third_party/requests/src/requests/models.py
platform/gsutil/third_party/requests/src/requests/packages.py
platform/gsutil/third_party/requests/src/requests/sessions.py
platform/gsutil/third_party/requests/src/requests/status_codes.py
platform/gsutil/third_party/requests/src/requests/structures.py
platform/gsutil/third_party/requests/src/requests/utils.py
platform/gsutil/third_party/requests/tests/__init__.py
platform/gsutil/third_party/requests/tests/certs/README.md
platform/gsutil/third_party/requests/tests/certs/expired/Makefile
platform/gsutil/third_party/requests/tests/certs/expired/README.md
platform/gsutil/third_party/requests/tests/certs/expired/ca/Makefile
platform/gsutil/third_party/requests/tests/certs/expired/ca/ca-private.key
platform/gsutil/third_party/requests/tests/certs/expired/ca/ca.cnf
platform/gsutil/third_party/requests/tests/certs/expired/ca/ca.crt
platform/gsutil/third_party/requests/tests/certs/expired/ca/ca.srl
platform/gsutil/third_party/requests/tests/certs/expired/server/Makefile
platform/gsutil/third_party/requests/tests/certs/expired/server/cert.cnf
platform/gsutil/third_party/requests/tests/certs/expired/server/server.csr
platform/gsutil/third_party/requests/tests/certs/expired/server/server.key
platform/gsutil/third_party/requests/tests/certs/expired/server/server.pem
platform/gsutil/third_party/requests/tests/certs/mtls/Makefile
platform/gsutil/third_party/requests/tests/certs/mtls/README.md
platform/gsutil/third_party/requests/tests/certs/mtls/client/Makefile
platform/gsutil/third_party/requests/tests/certs/mtls/client/ca
platform/gsutil/third_party/requests/tests/certs/mtls/client/cert.cnf
platform/gsutil/third_party/requests/tests/certs/mtls/client/client.csr
platform/gsutil/third_party/requests/tests/certs/mtls/client/client.key
platform/gsutil/third_party/requests/tests/certs/mtls/client/client.pem
platform/gsutil/third_party/requests/tests/certs/valid/ca
platform/gsutil/third_party/requests/tests/certs/valid/server/Makefile
platform/gsutil/third_party/requests/tests/certs/valid/server/cert.cnf
platform/gsutil/third_party/requests/tests/certs/valid/server/server.csr
platform/gsutil/third_party/requests/tests/certs/valid/server/server.key
platform/gsutil/third_party/requests/tests/certs/valid/server/server.pem
platform/gsutil/third_party/requests/tests/compat.py
platform/gsutil/third_party/requests/tests/conftest.py
platform/gsutil/third_party/requests/tests/test_adapters.py
platform/gsutil/third_party/requests/tests/test_help.py
platform/gsutil/third_party/requests/tests/test_hooks.py
platform/gsutil/third_party/requests/tests/test_lowlevel.py
platform/gsutil/third_party/requests/tests/test_packages.py
platform/gsutil/third_party/requests/tests/test_requests.py
platform/gsutil/third_party/requests/tests/test_structures.py
platform/gsutil/third_party/requests/tests/test_testserver.py
platform/gsutil/third_party/requests/tests/test_utils.py
platform/gsutil/third_party/requests/tests/testserver/__init__.py
platform/gsutil/third_party/requests/tests/testserver/server.py
platform/gsutil/third_party/requests/tests/utils.py
platform/gsutil/third_party/requests/tox.ini
platform/gsutil/third_party/retry-decorator/.gitignore
platform/gsutil/third_party/retry-decorator/CHANGES.txt
platform/gsutil/third_party/retry-decorator/LICENSE.txt
platform/gsutil/third_party/retry-decorator/MANIFEST.in
platform/gsutil/third_party/retry-decorator/Makefile
platform/gsutil/third_party/retry-decorator/README.rst
platform/gsutil/third_party/retry-decorator/retry_decorator/__init__.py
platform/gsutil/third_party/retry-decorator/retry_decorator/retry_decorator.py
platform/gsutil/third_party/retry-decorator/setup.py
platform/gsutil/third_party/rsa/.codeclimate.yml
platform/gsutil/third_party/rsa/.coveragerc
platform/gsutil/third_party/rsa/.gitignore
platform/gsutil/third_party/rsa/.travis.yml
platform/gsutil/third_party/rsa/CHANGELOG.txt
platform/gsutil/third_party/rsa/LICENSE
platform/gsutil/third_party/rsa/MANIFEST.in
platform/gsutil/third_party/rsa/Pipfile
platform/gsutil/third_party/rsa/Pipfile.lock
platform/gsutil/third_party/rsa/README.md
platform/gsutil/third_party/rsa/create_timing_table.py
platform/gsutil/third_party/rsa/doc/Makefile
platform/gsutil/third_party/rsa/doc/_build/.keep
platform/gsutil/third_party/rsa/doc/_static/.keep
platform/gsutil/third_party/rsa/doc/_templates/.keep
platform/gsutil/third_party/rsa/doc/cli.rst
platform/gsutil/third_party/rsa/doc/compatibility.rst
platform/gsutil/third_party/rsa/doc/conf.py
platform/gsutil/third_party/rsa/doc/index.rst
platform/gsutil/third_party/rsa/doc/installation.rst
platform/gsutil/third_party/rsa/doc/intro.rst
platform/gsutil/third_party/rsa/doc/licence.rst
platform/gsutil/third_party/rsa/doc/make.bat
platform/gsutil/third_party/rsa/doc/reference.rst
platform/gsutil/third_party/rsa/doc/upgrading.rst
platform/gsutil/third_party/rsa/doc/usage.rst
platform/gsutil/third_party/rsa/rsa/__init__.py
platform/gsutil/third_party/rsa/rsa/_compat.py
platform/gsutil/third_party/rsa/rsa/asn1.py
platform/gsutil/third_party/rsa/rsa/cli.py
platform/gsutil/third_party/rsa/rsa/common.py
platform/gsutil/third_party/rsa/rsa/core.py
platform/gsutil/third_party/rsa/rsa/key.py
platform/gsutil/third_party/rsa/rsa/machine_size.py
platform/gsutil/third_party/rsa/rsa/parallel.py
platform/gsutil/third_party/rsa/rsa/pem.py
platform/gsutil/third_party/rsa/rsa/pkcs1.py
platform/gsutil/third_party/rsa/rsa/pkcs1_v2.py
platform/gsutil/third_party/rsa/rsa/prime.py
platform/gsutil/third_party/rsa/rsa/randnum.py
platform/gsutil/third_party/rsa/rsa/transform.py
platform/gsutil/third_party/rsa/rsa/util.py
platform/gsutil/third_party/rsa/setup.cfg
platform/gsutil/third_party/rsa/setup.py
platform/gsutil/third_party/rsa/speed.sh
platform/gsutil/third_party/rsa/tox.ini
platform/gsutil/third_party/six/.github/workflows/ci.yml
platform/gsutil/third_party/six/.github/workflows/publish.yml
platform/gsutil/third_party/six/.gitignore
platform/gsutil/third_party/six/CHANGES
platform/gsutil/third_party/six/CONTRIBUTORS
platform/gsutil/third_party/six/LICENSE
platform/gsutil/third_party/six/MANIFEST.in
platform/gsutil/third_party/six/README.rst
platform/gsutil/third_party/six/documentation/Makefile
platform/gsutil/third_party/six/documentation/conf.py
platform/gsutil/third_party/six/documentation/index.rst
platform/gsutil/third_party/six/setup.cfg
platform/gsutil/third_party/six/setup.py
platform/gsutil/third_party/six/six.py
platform/gsutil/third_party/six/test_six.py
platform/gsutil/third_party/six/tox.ini
platform/gsutil/third_party/urllib3/.coveragerc
platform/gsutil/third_party/urllib3/.eslintrc.yml
platform/gsutil/third_party/urllib3/.github/CODEOWNERS
platform/gsutil/third_party/urllib3/.github/FUNDING.yml
platform/gsutil/third_party/urllib3/.github/ISSUE_TEMPLATE/01_feature_request.md
platform/gsutil/third_party/urllib3/.github/ISSUE_TEMPLATE/02_bug_report.md
platform/gsutil/third_party/urllib3/.github/ISSUE_TEMPLATE/config.yml
platform/gsutil/third_party/urllib3/.github/PULL_REQUEST_TEMPLATE.md
platform/gsutil/third_party/urllib3/.github/PULL_REQUEST_TEMPLATE/release.md
platform/gsutil/third_party/urllib3/.github/SECURITY.md
platform/gsutil/third_party/urllib3/.github/codeql.yml
platform/gsutil/third_party/urllib3/.github/dependabot.yml
platform/gsutil/third_party/urllib3/.github/workflows/changelog.yml
platform/gsutil/third_party/urllib3/.github/workflows/ci.yml
platform/gsutil/third_party/urllib3/.github/workflows/codeql.yml
platform/gsutil/third_party/urllib3/.github/workflows/downstream.yml
platform/gsutil/third_party/urllib3/.github/workflows/lint.yml
platform/gsutil/third_party/urllib3/.github/workflows/publish.yml
platform/gsutil/third_party/urllib3/.github/workflows/scorecards.yml
platform/gsutil/third_party/urllib3/.gitignore
platform/gsutil/third_party/urllib3/.pre-commit-config.yaml
platform/gsutil/third_party/urllib3/.readthedocs.yml
platform/gsutil/third_party/urllib3/CHANGES.rst
platform/gsutil/third_party/urllib3/CODE_OF_CONDUCT.md
platform/gsutil/third_party/urllib3/LICENSE.txt
platform/gsutil/third_party/urllib3/README.md
platform/gsutil/third_party/urllib3/changelog/.gitignore
platform/gsutil/third_party/urllib3/changelog/README.rst
platform/gsutil/third_party/urllib3/ci/deploy.sh
platform/gsutil/third_party/urllib3/dev-requirements.txt
platform/gsutil/third_party/urllib3/docs/Makefile
platform/gsutil/third_party/urllib3/docs/_static/banner.svg
platform/gsutil/third_party/urllib3/docs/_static/banner_github.svg
platform/gsutil/third_party/urllib3/docs/_static/dark-logo.svg
platform/gsutil/third_party/urllib3/docs/advanced-usage.rst
platform/gsutil/third_party/urllib3/docs/changelog.rst
platform/gsutil/third_party/urllib3/docs/conf.py
platform/gsutil/third_party/urllib3/docs/contributing.rst
platform/gsutil/third_party/urllib3/docs/images/demo-button.png
platform/gsutil/third_party/urllib3/docs/images/favicon.png
platform/gsutil/third_party/urllib3/docs/images/learn-more-button.png
platform/gsutil/third_party/urllib3/docs/images/logo.png
platform/gsutil/third_party/urllib3/docs/images/logo.svg
platform/gsutil/third_party/urllib3/docs/index.rst
platform/gsutil/third_party/urllib3/docs/make.bat
platform/gsutil/third_party/urllib3/docs/reference/contrib/emscripten.rst
platform/gsutil/third_party/urllib3/docs/reference/contrib/index.rst
platform/gsutil/third_party/urllib3/docs/reference/contrib/pyopenssl.rst
platform/gsutil/third_party/urllib3/docs/reference/contrib/socks.rst
platform/gsutil/third_party/urllib3/docs/reference/index.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.connection.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.connectionpool.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.exceptions.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.fields.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.poolmanager.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.request.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.response.rst
platform/gsutil/third_party/urllib3/docs/reference/urllib3.util.rst
platform/gsutil/third_party/urllib3/docs/requirements.txt
platform/gsutil/third_party/urllib3/docs/sponsors.rst
platform/gsutil/third_party/urllib3/docs/user-guide.rst
platform/gsutil/third_party/urllib3/docs/v2-migration-guide.rst
platform/gsutil/third_party/urllib3/dummyserver/__init__.py
platform/gsutil/third_party/urllib3/dummyserver/app.py
platform/gsutil/third_party/urllib3/dummyserver/asgi_proxy.py
platform/gsutil/third_party/urllib3/dummyserver/certs/README.rst
platform/gsutil/third_party/urllib3/dummyserver/certs/cacert.key
platform/gsutil/third_party/urllib3/dummyserver/certs/cacert.pem
platform/gsutil/third_party/urllib3/dummyserver/certs/server.crt
platform/gsutil/third_party/urllib3/dummyserver/certs/server.key
platform/gsutil/third_party/urllib3/dummyserver/hypercornserver.py
platform/gsutil/third_party/urllib3/dummyserver/socketserver.py
platform/gsutil/third_party/urllib3/dummyserver/testcase.py
platform/gsutil/third_party/urllib3/emscripten-requirements.txt
platform/gsutil/third_party/urllib3/mypy-requirements.txt
platform/gsutil/third_party/urllib3/notes/connection-lifecycle.md
platform/gsutil/third_party/urllib3/notes/public-and-private-apis.md
platform/gsutil/third_party/urllib3/noxfile.py
platform/gsutil/third_party/urllib3/pyproject.toml
platform/gsutil/third_party/urllib3/setup.cfg
platform/gsutil/third_party/urllib3/src/urllib3/__init__.py
platform/gsutil/third_party/urllib3/src/urllib3/_base_connection.py
platform/gsutil/third_party/urllib3/src/urllib3/_collections.py
platform/gsutil/third_party/urllib3/src/urllib3/_request_methods.py
platform/gsutil/third_party/urllib3/src/urllib3/_version.py
platform/gsutil/third_party/urllib3/src/urllib3/connection.py
platform/gsutil/third_party/urllib3/src/urllib3/connectionpool.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/__init__.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/emscripten/__init__.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/emscripten/connection.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/emscripten/emscripten_fetch_worker.js
platform/gsutil/third_party/urllib3/src/urllib3/contrib/emscripten/fetch.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/emscripten/request.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/emscripten/response.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/pyopenssl.py
platform/gsutil/third_party/urllib3/src/urllib3/contrib/socks.py
platform/gsutil/third_party/urllib3/src/urllib3/exceptions.py
platform/gsutil/third_party/urllib3/src/urllib3/fields.py
platform/gsutil/third_party/urllib3/src/urllib3/filepost.py
platform/gsutil/third_party/urllib3/src/urllib3/http2.py
platform/gsutil/third_party/urllib3/src/urllib3/poolmanager.py
platform/gsutil/third_party/urllib3/src/urllib3/py.typed
platform/gsutil/third_party/urllib3/src/urllib3/response.py
platform/gsutil/third_party/urllib3/src/urllib3/util/__init__.py
platform/gsutil/third_party/urllib3/src/urllib3/util/connection.py
platform/gsutil/third_party/urllib3/src/urllib3/util/proxy.py
platform/gsutil/third_party/urllib3/src/urllib3/util/request.py
platform/gsutil/third_party/urllib3/src/urllib3/util/response.py
platform/gsutil/third_party/urllib3/src/urllib3/util/retry.py
platform/gsutil/third_party/urllib3/src/urllib3/util/ssl_.py
platform/gsutil/third_party/urllib3/src/urllib3/util/ssl_match_hostname.py
platform/gsutil/third_party/urllib3/src/urllib3/util/ssltransport.py
platform/gsutil/third_party/urllib3/src/urllib3/util/timeout.py
platform/gsutil/third_party/urllib3/src/urllib3/util/url.py
platform/gsutil/third_party/urllib3/src/urllib3/util/util.py
platform/gsutil/third_party/urllib3/src/urllib3/util/wait.py
platform/gsutil/third_party/urllib3/test/__init__.py
platform/gsutil/third_party/urllib3/test/conftest.py
platform/gsutil/third_party/urllib3/test/contrib/__init__.py
platform/gsutil/third_party/urllib3/test/contrib/duplicate_san.pem
platform/gsutil/third_party/urllib3/test/contrib/emscripten/__init__.py
platform/gsutil/third_party/urllib3/test/contrib/emscripten/conftest.py
platform/gsutil/third_party/urllib3/test/contrib/emscripten/templates/pyodide-console.html
platform/gsutil/third_party/urllib3/test/contrib/emscripten/test_emscripten.py
platform/gsutil/third_party/urllib3/test/contrib/test_pyopenssl.py
platform/gsutil/third_party/urllib3/test/contrib/test_pyopenssl_dependencies.py
platform/gsutil/third_party/urllib3/test/contrib/test_socks.py
platform/gsutil/third_party/urllib3/test/port_helpers.py
platform/gsutil/third_party/urllib3/test/test_collections.py
platform/gsutil/third_party/urllib3/test/test_compatibility.py
platform/gsutil/third_party/urllib3/test/test_connection.py
platform/gsutil/third_party/urllib3/test/test_connectionpool.py
platform/gsutil/third_party/urllib3/test/test_exceptions.py
platform/gsutil/third_party/urllib3/test/test_fields.py
platform/gsutil/third_party/urllib3/test/test_filepost.py
platform/gsutil/third_party/urllib3/test/test_no_ssl.py
platform/gsutil/third_party/urllib3/test/test_poolmanager.py
platform/gsutil/third_party/urllib3/test/test_proxymanager.py
platform/gsutil/third_party/urllib3/test/test_queue_monkeypatch.py
platform/gsutil/third_party/urllib3/test/test_response.py
platform/gsutil/third_party/urllib3/test/test_retry.py
platform/gsutil/third_party/urllib3/test/test_ssl.py
platform/gsutil/third_party/urllib3/test/test_ssltransport.py
platform/gsutil/third_party/urllib3/test/test_util.py
platform/gsutil/third_party/urllib3/test/test_wait.py
platform/gsutil/third_party/urllib3/test/tz_stub.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/__init__.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_chunked_transfer.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_connection.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_connectionpool.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_https.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_no_ssl.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_poolmanager.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_proxy_poolmanager.py
platform/gsutil/third_party/urllib3/test/with_dummyserver/test_socketlevel.py
platform/gsutil/third_party/urllib3/towncrier.toml
