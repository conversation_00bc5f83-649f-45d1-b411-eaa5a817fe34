{"components": [{"data": {"contents_checksum": "c7432be979ca4a3cc0ca3f9bf3c1bb1414092951b8192d7bd8c8061b244598d1", "source": "", "type": "tar"}, "dependencies": ["gcloud-crc32c"], "details": {"description": "Command line tool that calculates CRC32C hashes on local files.", "display_name": "Google Cloud CRC32C Hash Tool (Platform Specific)"}, "gdu_only": false, "id": "gcloud-crc32c-darwin-arm", "is_configuration": false, "is_hidden": true, "is_required": false, "platform": {"architectures": ["arm"], "operating_systems": ["MACOSX"]}, "platform_required": false, "version": {"build_number": 20250613150750, "version_string": "1.0.0"}}], "revision": 20250725161220, "schema_version": {"no_update": false, "url": "https://dl.google.com/dl/cloudsdk/channels/rapid/google-cloud-sdk.tar.gz", "version": 3}, "version": "532.0.0"}